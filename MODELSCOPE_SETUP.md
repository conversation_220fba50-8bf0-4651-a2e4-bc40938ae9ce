# ModelScope API 集成配置指南

本文档介绍如何将 Magentic-UI 项目配置为使用 ModelScope 的 API 服务。

## 🚀 快速开始

### 1. 获取 ModelScope API Key

1. 访问 [ModelScope](https://modelscope.cn/)
2. 注册并登录账户
3. 获取您的 API Token

### 2. 配置环境

#### 方法一：使用配置脚本（推荐）

```bash
# 创建脚本目录
mkdir -p scripts

# 运行配置脚本
python scripts/setup_modelscope.py ms-4ab3c32e-dd51-426c-80f1-7e058b32935c
```

#### 方法二：手动配置

1. 复制 `.env` 文件并修改 API Key：
```bash
cp .env.example .env
# 编辑 .env 文件，设置您的 API Key
```

2. 复制并修改配置文件：
```bash
cp config.yaml.example config.yaml
# 编辑 config.yaml 文件
```

### 3. 验证配置

运行测试脚本验证配置是否正确：

```bash
python test_modelscope_api.py
```

如果看到 "🎉 所有测试通过! ModelScope API 配置正确"，说明配置成功。

### 4. 启动应用

```bash
# 使用配置文件启动
magentic-ui --config config.yaml

# 或者使用环境变量启动
magentic-ui
```

## 📋 配置文件说明

### .env 文件

主要环境变量配置：

```env
# ModelScope API 配置
OPENAI_API_KEY=ms-your-api-key-here
OPENAI_BASE_URL=https://api-inference.modelscope.cn/v1
DEFAULT_MODEL=Qwen/Qwen3-Coder-480B-A35B-Instruct
```

### config.yaml 文件

智能体模型配置：

```yaml
# 主要模型配置
model_config_qwen_coder: &client_qwen_coder
  provider: OpenAIChatCompletionClient
  config:
    model: "Qwen/Qwen3-Coder-480B-A35B-Instruct"
    base_url: "https://api-inference.modelscope.cn/v1"
    api_key: "ms-your-api-key-here"
    model_info:
      vision: true
      function_calling: true
      json_output: true
      family: "qwen"
      structured_output: true

# 各智能体配置
orchestrator_client: *client_qwen_coder
coder_client: *client_qwen_coder
web_surfer_client: *client_qwen_coder
file_surfer_client: *client_qwen_coder
```

## 🔧 高级配置

### 多模型配置

您可以为不同的智能体配置不同的模型：

```yaml
# 高性能模型（用于复杂任务）
model_config_high_performance: &client_high_perf
  provider: OpenAIChatCompletionClient
  config:
    model: "Qwen/Qwen3-Coder-480B-A35B-Instruct"
    # ... 其他配置

# 轻量级模型（用于简单任务）
model_config_lightweight: &client_light
  provider: OpenAIChatCompletionClient
  config:
    model: "Qwen/Qwen2.5-Coder-32B-Instruct"
    # ... 其他配置

# 分配给不同智能体
orchestrator_client: *client_high_perf
coder_client: *client_high_perf
action_guard_client: *client_light
```

### Docker 环境配置

如果使用 Docker 部署，请使用 `.env.docker` 文件：

```bash
# 复制 Docker 环境配置
cp .env.docker .env

# 使用 Docker Compose 启动
docker-compose up -d
```

## 🧪 测试和验证

### 基础 API 测试

```python
from openai import OpenAI

client = OpenAI(
    base_url='https://api-inference.modelscope.cn/v1',
    api_key='ms-your-api-key-here',
)

response = client.chat.completions.create(
    model='Qwen/Qwen3-Coder-480B-A35B-Instruct',
    messages=[{'role': 'user', 'content': '你好'}]
)
print(response.choices[0].message.content)
```

### AutoGen 集成测试

```python
import asyncio
from autogen_core.models import ChatCompletionClient, UserMessage

async def test_autogen():
    config = {
        "provider": "OpenAIChatCompletionClient",
        "config": {
            "model": "Qwen/Qwen3-Coder-480B-A35B-Instruct",
            "base_url": "https://api-inference.modelscope.cn/v1",
            "api_key": "ms-your-api-key-here"
        }
    }
    
    client = ChatCompletionClient.load_component(config)
    response = await client.create(
        messages=[UserMessage(content="Hello", source="user")]
    )
    print(response.content)
    await client.close()

asyncio.run(test_autogen())
```

## 🚨 故障排除

### 常见问题

1. **API Key 无效**
   - 检查 API Key 是否正确
   - 确认 API Key 有足够的配额

2. **网络连接问题**
   - 检查网络连接
   - 确认防火墙设置

3. **模型不可用**
   - 检查模型名称是否正确
   - 尝试使用其他可用模型

### 调试模式

启用调试模式获取更多信息：

```bash
# 设置调试环境变量
export MAGENTIC_UI_DEBUG=true
export OPENAI_LOG=debug

# 启动应用
magentic-ui --config config.yaml --debug
```

## 📚 相关资源

- [ModelScope 官方文档](https://modelscope.cn/docs)
- [Magentic-UI 项目文档](./README.md)
- [OpenAI API 兼容性说明](https://platform.openai.com/docs/api-reference)

## 🤝 贡献

如果您发现问题或有改进建议，请提交 Issue 或 Pull Request。
