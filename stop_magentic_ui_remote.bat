@echo off
setlocal

echo ========================================
echo 停止 Magentic-UI (远程 Docker 模式)
echo ========================================

REM 设置环境变量文件
echo 加载环境变量文件: .env.remote

REM 检查环境变量文件是否存在
if not exist ".env.remote" (
    echo ⚠️  环境变量文件 .env.remote 不存在!
    echo 尝试使用默认的 Docker 主机设置...
    set "DOCKER_HOST=tcp://************:2375"
) else (
    REM 加载环境变量
    for /f "usebackq tokens=*" %%a in (`.env.remote`) do (
        set "line=%%a"
        if not "!line:~0,1!"=="#" if "!line!" neq "" (
            set "!line!"
        )
    )
)

echo 当前 DOCKER_HOST=%DOCKER_HOST%

REM 检查 Docker 连接
echo.
echo 正在检查 Docker 连接...
docker version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Docker 连接正常
) else (
    echo ⚠️  Docker 连接失败，但仍尝试停止服务...
)

REM 检查 docker-compose 文件是否存在
if not exist "docker-compose.remote.yml" (
    echo ❌ docker-compose.remote.yml 文件不存在!
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

REM 停止 Magentic-UI 服务
echo.
echo 正在停止 Magentic-UI 服务...
echo 使用配置文件: docker-compose.remote.yml

docker-compose -f docker-compose.remote.yml down

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Magentic-UI 服务已停止!
    echo.
    echo 已停止的容器:
    docker-compose -f docker-compose.remote.yml ps
) else (
    echo.
    echo ⚠️  停止服务时出现错误，请手动检查服务状态
    echo.
    echo 查看当前容器状态:
    docker-compose -f docker-compose.remote.yml ps
)

REM 询问是否要删除数据卷
echo.
echo 是否要删除数据卷？这将清除所有数据！
echo 注意：此操作不可恢复！
set /p DELETE_VOLUMES=请输入 y 或 n (默认为 n): 

if /i "!DELETE_VOLUMES!"=="y" (
    echo.
    echo 正在删除数据卷...
    docker-compose -f docker-compose.remote.yml down -v
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ 数据卷已删除!
    ) else (
        echo ⚠️  删除数据卷时出现错误
    )
    
    REM 删除本地数据目录
    if exist "data" (
        echo 删除本地数据目录: data
        rmdir /s /q data
    )
)

echo.
echo ========================================
echo 停止脚本执行完成
echo ========================================
pause
