#!/usr/bin/env python3
"""
使用 Docker API 检查远程 Magentic-UI 服务状态
"""

import requests
import json

# 远程 Docker API 地址
DOCKER_API_URL = "http://8.137.154.11:2375"

def check_docker_api():
    """检查 Docker API 是否可用"""
    try:
        response = requests.get(f"{DOCKER_API_URL}/version", timeout=10)
        if response.status_code == 200:
            version_info = response.json()
            print("✅ Docker API 连接成功!")
            print(f"Docker 版本: {version_info.get('Version', '未知')}")
            print(f"API 版本: {version_info.get('ApiVersion', '未知')}")
            return True
        else:
            print(f"❌ Docker API 连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Docker API 连接出错: {e}")
        return False

def check_container_status(container_name):
    """检查容器状态"""
    try:
        response = requests.get(
            f"{DOCKER_API_URL}/containers/{container_name}/json",
            timeout=10
        )
        
        if response.status_code == 200:
            container_info = response.json()
            state = container_info['State']
            status = state['Status']
            running = state['Running']
            
            print(f"容器名称: {container_name}")
            print(f"状态: {status}")
            print(f"运行中: {'是' if running else '否'}")
            print(f"启动时间: {state.get('StartedAt', 'N/A')}")
            print(f"镜像: {container_info.get('Image', 'N/A')}")
            
            # 显示端口映射
            ports = container_info.get('NetworkSettings', {}).get('Ports', {})
            if ports:
                print("端口映射:")
                for container_port, host_ports in ports.items():
                    if host_ports:
                        host_port = host_ports[0]['HostPort']
                        print(f"  {container_port} -> {host_port}")
                    else:
                        print(f"  {container_port} -> 未映射")
            
            return running
        elif response.status_code == 404:
            print(f"容器 {container_name} 不存在")
            return False
        else:
            print(f"获取容器 {container_name} 状态失败: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"获取容器 {container_name} 状态出错: {e}")
        return False

def list_all_containers():
    """列出所有容器"""
    try:
        response = requests.get(
            f"{DOCKER_API_URL}/containers/json?all=true",
            timeout=10
        )
        
        if response.status_code == 200:
            containers = response.json()
            print(f"\n服务器上共有 {len(containers)} 个容器:")
            
            if containers:
                for container in containers:
                    names = container.get('Names', ['N/A'])
                    name = names[0] if names else 'N/A'
                    # 移除前导的 "/"
                    name = name[1:] if name.startswith('/') else name
                    
                    status = container.get('Status', 'N/A')
                    image = container.get('Image', 'N/A')
                    
                    print(f"  - {name}: {status} (镜像: {image})")
            else:
                print("  没有找到容器")
                
            return True
        else:
            print(f"获取容器列表失败: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"获取容器列表出错: {e}")
        return False

def main():
    """主函数"""
    print("Magentic-UI 远程服务状态检查")
    print("=" * 60)
    
    # 检查 Docker API 连接
    if not check_docker_api():
        print("❌ 无法连接到远程 Docker API，请检查配置")
        return 1
    
    # 检查特定容器状态
    print("\n" + "=" * 50)
    print("检查 Magentic-UI 容器状态")
    print("=" * 50)
    magentic_ui_running = check_container_status("magentic-ui")
    
    print("\n" + "=" * 50)
    print("检查浏览器容器状态")
    print("=" * 50)
    browser_running = check_container_status("magentic-ui-browser")
    
    # 列出所有容器
    print("\n" + "=" * 50)
    print("服务器上所有容器")
    print("=" * 50)
    list_all_containers()
    
    print("\n" + "=" * 60)
    if magentic_ui_running and browser_running:
        print("🎉 所有服务都在正常运行!")
    elif magentic_ui_running or browser_running:
        print("⚠️  部分服务在运行:")
        if magentic_ui_running:
            print("  - Magentic-UI 服务: 运行中")
        if browser_running:
            print("  - 浏览器服务: 运行中")
    else:
        print("ℹ️  服务未运行或已停止")
    
    print("\n管理命令:")
    print("- 启动服务: python start_remote_services.py")
    print("- 停止服务: python stop_remote_services.py")
    print("- 查看日志: python view_remote_logs.py")
    
    return 0

if __name__ == "__main__":
    exit(main())
