# Magentic-UI 配置文件
model_config_main: &main_model
  provider: OpenAIChatCompletionClient
  config:
    model: "Qwen/Qwen3-Coder-480B-A35B-Instruct"
    base_url: "https://api-inference.modelscope.cn/v1"
    api_key: "ms-4ab3c32e-dd51-426c-80f1-7e058b32935c"
    model_info:
      vision: true
      function_calling: true
      json_output: true
      family: "qwen"
      structured_output: true
  max_retries: 5

model_config_light: &light_model
  provider: OpenAIChatCompletionClient
  config:
    model: "Qwen/Qwen2.5-Coder-32B-Instruct"
    base_url: "https://api-inference.modelscope.cn/v1"
    api_key: "ms-4ab3c32e-dd51-426c-80f1-7e058b32935c"
    model_info:
      vision: false
      function_calling: true
      json_output: true
      family: "qwen"
  max_retries: 3

# 智能体配置
orchestrator_client: *main_model
coder_client: *main_model
web_surfer_client: *main_model
file_surfer_client: *main_model
action_guard_client: *light_model
user_proxy_client: *main_model
model_client: *main_model
