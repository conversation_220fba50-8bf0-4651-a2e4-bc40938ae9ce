# Magentic-UI Configuration for ModelScope API
# 基于 ModelScope API 的配置文件

# 主要模型配置 - 使用 ModelScope 的 Qwen3-Coder 模型
model_config_qwen_coder: &client_qwen_coder
  provider: OpenAIChatCompletionClient
  config:
    model: "Qwen/Qwen3-Coder-480B-A35B-Instruct"
    base_url: "https://api-inference.modelscope.cn/v1"
    api_key: "ms-4ab3c32e-dd51-426c-80f1-7e058b32935c"
    model_info:
      vision: true
      function_calling: true
      json_output: true
      family: "qwen"
      structured_output: true
      multiple_system_messages: true
  max_retries: 10

# 轻量级模型配置 - 用于 action guard 等轻量任务
model_config_qwen_light: &client_qwen_light
  provider: OpenAIChatCompletionClient
  config:
    model: "Qwen/Qwen2.5-Coder-32B-Instruct"
    base_url: "https://api-inference.modelscope.cn/v1"
    api_key: "ms-4ab3c32e-dd51-426c-80f1-7e058b32935c"
    model_info:
      vision: false
      function_calling: true
      json_output: true
      family: "qwen"
      structured_output: true
      multiple_system_messages: true
  max_retries: 5

# 各个智能体的模型配置
orchestrator_client: *client_qwen_coder
coder_client: *client_qwen_coder
web_surfer_client: *client_qwen_coder
file_surfer_client: *client_qwen_coder
action_guard_client: *client_qwen_light
user_proxy_client: *client_qwen_coder
model_client: *client_qwen_coder

# 计划学习配置
plan_learning_client: *client_qwen_coder

# 评估配置
evaluation_client: *client_qwen_coder
