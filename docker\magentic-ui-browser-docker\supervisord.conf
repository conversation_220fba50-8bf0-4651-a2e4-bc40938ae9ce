[supervisord]
nodaemon=true
logfile=/var/log/supervisord.log
logfile_maxbytes=50MB
loglevel=info

[program:xvfb]
command=Xvfb :99 -screen 0 1440x1440x24 -ac -nolisten tcp
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:openbox]
command=openbox-session
environment=DISPLAY=:99
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:x11setup]
command=/app/x11-setup.sh
environment=DISPLAY=:99
autorestart=false
startsecs=0
startretries=0
priority=10
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:x11vnc]
command=x11vnc -display :99 -forever -shared -nopw -geometry 1440x1440 -scale 1:1 -nomodtweak
autorestart=true
priority=20
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:novnc]
command=/usr/local/novnc/utils/novnc_proxy --vnc localhost:5900 --listen %(ENV_NO_VNC_PORT)s
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:playwright-server]
command=node /app/playwright-server.js
environment=DISPLAY=:99,WS_PATH=%(ENV_PLAYWRIGHT_WS_PATH)s,PLAYWRIGHT_PORT=%(ENV_PLAYWRIGHT_PORT)s
autorestart=true
priority=30
startsecs=1
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
