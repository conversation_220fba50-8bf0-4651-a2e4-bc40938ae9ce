#!/usr/bin/env python3
"""
测试远程 Docker 连接和 Magentic-UI 功能
"""

import os
import sys
import subprocess
import requests
from pathlib import Path

def test_docker_connection():
    """测试 Docker 连接"""
    print("=" * 50)
    print("测试远程 Docker 连接")
    print("=" * 50)
    
    # 设置环境变量
    os.environ['DOCKER_HOST'] = 'tcp://************:2375'
    print(f"DOCKER_HOST 设置为: {os.environ.get('DOCKER_HOST', '未设置')}")
    
    # 检查是否安装了 Docker CLI
    try:
        docker_check = subprocess.run(['docker', '--version'], 
                                    capture_output=True, text=True, timeout=5)
        if docker_check.returncode != 0:
            print("⚠️  未检测到 Docker CLI 工具")
            print("   请安装 Docker Desktop 或 Docker CLI 工具以进行完整的 Docker 测试")
            print("   但您仍然可以使用 docker-compose.remote.yml 配置文件来启动服务")
            return False
    except FileNotFoundError:
        print("⚠️  未找到 Docker CLI 工具 (docker 命令)")
        print("   请安装 Docker Desktop 或 Docker CLI 工具以进行完整的 Docker 测试")
        print("   但您仍然可以使用 docker-compose.remote.yml 配置文件来启动服务")
        return False
    except Exception as e:
        print(f"⚠️  检查 Docker CLI 时出错: {e}")
        return False
    
    try:
        # 测试 Docker 连接
        result = subprocess.run(['docker', 'version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Docker 连接成功!")
            print(result.stdout)
            return True
        else:
            print("❌ Docker 连接失败!")
            print("错误信息:")
            print(result.stderr)
            print("\n请检查:")
            print("1. 远程服务器 Docker 服务是否正在运行")
            print("2. 防火墙是否允许端口 2375 通信")
            print("3. 服务器 Docker 配置是否正确")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Docker 连接超时!")
        print("请检查网络连接和远程服务器状态")
        return False
    except Exception as e:
        print(f"❌ Docker 连接出错: {e}")
        return False

def test_docker_info():
    """获取 Docker 信息"""
    print("\n" + "=" * 50)
    print("获取 Docker 信息")
    print("=" * 50)
    
    try:
        result = subprocess.run(['docker', 'info'], 
                              capture_output=True, text=True, timeout=15)
        if result.returncode == 0:
            print("✅ Docker 信息获取成功!")
            # 只显示部分信息以避免输出过长
            lines = result.stdout.split('\n')
            for line in lines[:20]:  # 只显示前20行
                print(line)
            if len(lines) > 20:
                print("... (信息已截断)")
            return True
        else:
            print("❌ Docker 信息获取失败!")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 获取 Docker 信息出错: {e}")
        return False

def test_modelscope_api():
    """测试 ModelScope API"""
    print("\n" + "=" * 50)
    print("测试 ModelScope API 连接")
    print("=" * 50)
    
    try:
        # 读取配置文件
        config_path = Path("config.yaml")
        if not config_path.exists():
            print("❌ 配置文件 config.yaml 不存在!")
            return False
            
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 获取 API 配置
        model_config = config['model_config_light']
        api_key = model_config['config']['api_key']
        base_url = model_config['config']['base_url']
        model = model_config['config']['model']
        
        print(f"Base URL: {base_url}")
        print(f"Model: {model}")
        
        # 测试 API 调用
        response = requests.post(
            f"{base_url}/chat/completions",
            headers={
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            },
            json={
                'model': model,
                'messages': [
                    {'role': 'user', 'content': 'Hello, please respond with "API test successful"'}
                ],
                'max_tokens': 20
            },
            timeout=30
        )
        
        print(f"HTTP Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API 测试成功!")
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"响应内容: {content}")
            return True
        else:
            print(f"❌ API 测试失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API 测试出错: {e}")
        return False

def test_docker_images():
    """测试 Docker 镜像拉取"""
    print("\n" + "=" * 50)
    print("测试 Docker 镜像")
    print("=" * 50)
    
    images = [
        "ghcr.io/microsoft/magentic-ui-python-env:0.0.1",
        "ghcr.io/microsoft/magentic-ui-browser:0.0.1"
    ]
    
    for image in images:
        print(f"\n检查镜像: {image}")
        try:
            # 检查镜像是否存在
            result = subprocess.run(['docker', 'images', image], 
                                  capture_output=True, text=True, timeout=10)
            if image in result.stdout:
                print(f"✅ 镜像 {image} 已存在")
            else:
                print(f"ℹ️  镜像 {image} 不存在，需要拉取")
                # 尝试拉取镜像
                print(f"正在拉取镜像 {image}...")
                pull_result = subprocess.run(['docker', 'pull', image], 
                                           capture_output=True, text=True, timeout=120)
                if pull_result.returncode == 0:
                    print(f"✅ 镜像 {image} 拉取成功")
                else:
                    print(f"❌ 镜像 {image} 拉取失败: {pull_result.stderr}")
        except Exception as e:
            print(f"❌ 检查镜像 {image} 出错: {e}")

def test_docker_containers():
    """测试 Docker 容器运行"""
    print("\n" + "=" * 50)
    print("测试 Docker 容器")
    print("=" * 50)
    
    try:
        # 列出正在运行的容器
        result = subprocess.run(['docker', 'ps'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ 正在运行的容器:")
            print(result.stdout)
        else:
            print("❌ 获取容器列表失败!")
            print(result.stderr)
    except Exception as e:
        print(f"❌ 获取容器列表出错: {e}")

def main():
    """主函数"""
    print("_magentic-ui 远程 Docker 测试")
    print("=" * 60)
    
    # 测试 Docker 连接
    docker_ok = test_docker_connection()
    
    if docker_ok:
        # 获取 Docker 信息
        test_docker_info()
        
        # 测试 Docker 镜像
        test_docker_images()
        
        # 测试 Docker 容器
        test_docker_containers()
    
    # 测试 ModelScope API
    api_ok = test_modelscope_api()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"Docker 连接: {'✅ 成功' if docker_ok else '❌ 失败'}")
    print(f"ModelScope API: {'✅ 成功' if api_ok else '❌ 失败'}")
    
    if docker_ok and api_ok:
        print("\n🎉 所有测试通过! 您可以开始使用 Magentic-UI 了。")
        print("\n下一步建议:")
        print("1. 使用 docker-compose.remote.yml 启动服务:")
        print("   docker-compose -f docker-compose.remote.yml up")
        print("2. 访问 http://localhost:8081 使用 Magentic-UI")
    else:
        print("\n⚠️  部分测试失败，请检查配置。")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
