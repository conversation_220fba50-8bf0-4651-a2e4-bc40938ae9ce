#!/usr/bin/env python3
"""
测试 Magentic-UI Web 服务启动
"""

import os
import sys
import asyncio
import time
import subprocess
import requests
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_web_server_start():
    """测试 Web 服务启动"""
    print("测试 Web 服务启动...")
    
    try:
        # 尝试启动 Web 服务（非阻塞）
        cmd = [
            "magentic-env\\Scripts\\python", 
            "-m", "magentic_ui.backend.cli",
            "--config", "config.yaml",
            "--host", "127.0.0.1",
            "--port", "8081"
        ]
        
        print(f"启动命令: {' '.join(cmd)}")
        
        # 启动服务进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        print("✅ Web 服务进程已启动")
        print(f"✅ 进程 ID: {process.pid}")
        
        # 等待服务启动
        print("等待服务启动...")
        max_wait = 30  # 最多等待30秒
        wait_time = 0
        
        while wait_time < max_wait:
            try:
                # 检查服务是否响应
                response = requests.get("http://127.0.0.1:8081", timeout=5)
                if response.status_code == 200:
                    print("✅ Web 服务启动成功！")
                    print(f"✅ 服务地址: http://127.0.0.1:8081")
                    
                    # 终止进程
                    process.terminate()
                    process.wait(timeout=5)
                    print("✅ 服务已正常关闭")
                    return True
                    
            except requests.exceptions.RequestException:
                # 服务还未启动，继续等待
                time.sleep(2)
                wait_time += 2
                print(f"等待中... ({wait_time}/{max_wait}s)")
        
        # 超时，终止进程
        print("❌ 服务启动超时")
        process.terminate()
        process.wait(timeout=5)
        
        # 读取错误输出
        stdout, stderr = process.communicate()
        if stderr:
            print(f"错误输出: {stderr}")
        
        return False
        
    except Exception as e:
        print(f"❌ Web 服务启动失败: {e}")
        return False

def test_api_endpoints():
    """测试 API 端点（需要服务运行）"""
    print("\n测试 API 端点...")
    
    # 这个测试需要服务运行，暂时跳过
    print("⏭️  API 端点测试跳过（需要服务运行）")
    return True

def test_frontend_files():
    """测试前端文件"""
    print("\n测试前端文件...")
    
    try:
        frontend_dir = Path("frontend")
        if not frontend_dir.exists():
            print("❌ frontend 目录不存在")
            return False
        
        # 检查关键文件
        key_files = [
            "package.json",
            "gatsby-config.ts",
            "src"
        ]
        
        missing_files = []
        for file in key_files:
            if not (frontend_dir / file).exists():
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ 缺少前端文件: {missing_files}")
            return False
        
        print("✅ 前端文件检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 前端文件检查失败: {e}")
        return False

def test_database_setup():
    """测试数据库设置"""
    print("\n测试数据库设置...")
    
    try:
        # 检查数据库文件是否会被创建
        db_path = Path("magentic_ui.db")
        
        # 如果数据库文件存在，说明之前已经初始化过
        if db_path.exists():
            print("✅ 数据库文件已存在")
            return True
        
        print("✅ 数据库将在首次启动时创建")
        return True
        
    except Exception as e:
        print(f"❌ 数据库设置检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Magentic-UI Web 服务测试")
    print("=" * 50)
    
    results = []
    
    # 测试前端文件
    results.append(test_frontend_files())
    
    # 测试数据库设置
    results.append(test_database_setup())
    
    # 测试 Web 服务启动
    results.append(test_web_server_start())
    
    # 测试 API 端点
    results.append(test_api_endpoints())
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 50)
    print("Web 服务测试总结")
    print("=" * 50)
    print(f"通过测试: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 Web 服务测试全部通过！")
        print("✅ Magentic-UI 可以正常启动")
        print("\n📋 下一步:")
        print("1. 运行启动脚本: start_magentic_ui.bat")
        print("2. 访问: http://127.0.0.1:8081")
        print("3. 开始使用 Magentic-UI")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
