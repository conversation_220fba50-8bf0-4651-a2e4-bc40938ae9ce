# Docker 环境配置
# 基于 ModelScope 的 OpenAI 兼容 API 配置
OPENAI_API_KEY=ms-4ab3c32e-dd51-426c-80f1-7e058b32935c
OPENAI_BASE_URL=https://api-inference.modelscope.cn/v1

# 默认模型配置
DEFAULT_MODEL=Qwen/Qwen3-Coder-480B-A35B-Instruct

# Docker 特定配置
INSIDE_DOCKER=1
RUN_WITHOUT_DOCKER=false
INTERNAL_WORKSPACE_ROOT=/workspace
EXTERNAL_WORKSPACE_ROOT=/workspace

# Magentic-UI 配置
MAGENTIC_UI_DATABASE_URI=sqlite:///./magentic_ui.db
MAGENTIC_UI_API_DOCS=false
MAGENTIC_UI_CLEANUP_INTERVAL=300
MAGENTIC_UI_SESSION_TIMEOUT=360000
MAGENTIC_UI_CONFIG_DIR=configs
MAGENTIC_UI_DEFAULT_USER_ID=<EMAIL>
MAGENTIC_UI_UPGRADE_DATABASE=false

# Docker 镜像配置
MAGENTIC_UI_BROWSER_IMAGE=ghcr.io/microsoft/magentic-ui-browser:0.0.1
MAGENTIC_UI_PYTHON_IMAGE=ghcr.io/microsoft/magentic-ui-python-env:0.0.1
