#!/usr/bin/env python3
"""
使用 Docker API 查看远程 Magentic-UI 服务日志
"""

import requests
import sys

# 远程 Docker API 地址
DOCKER_API_URL = "http://8.137.154.11:2375"

def check_docker_api():
    """检查 Docker API 是否可用"""
    try:
        response = requests.get(f"{DOCKER_API_URL}/version", timeout=10)
        if response.status_code == 200:
            version_info = response.json()
            print("✅ Docker API 连接成功!")
            print(f"Docker 版本: {version_info.get('Version', '未知')}")
            return True
        else:
            print(f"❌ Docker API 连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Docker API 连接出错: {e}")
        return False

def get_container_logs(container_name, tail=50):
    """获取容器日志"""
    try:
        # 首先检查容器是否存在
        response = requests.get(
            f"{DOCKER_API_URL}/containers/{container_name}/json",
            timeout=10
        )
        
        if response.status_code == 404:
            print(f"⚠️  容器 {container_name} 不存在")
            return False
        elif response.status_code != 200:
            print(f"❌ 检查容器 {container_name} 失败: {response.status_code}")
            print(response.text)
            return False
        
        # 获取日志
        params = {
            'tail': tail,
            'stdout': 'true',
            'stderr': 'true',
            'timestamps': 'true'
        }
        
        response = requests.get(
            f"{DOCKER_API_URL}/containers/{container_name}/logs",
            params=params,
            timeout=30
        )
        
        if response.status_code == 200:
            logs = response.text
            if logs:
                print(f"\n{container_name} 容器最近 {tail} 行日志:")
                print("=" * 60)
                print(logs)
                print("=" * 60)
            else:
                print(f"\n{container_name} 容器没有日志输出")
            return True
        else:
            print(f"❌ 获取 {container_name} 容器日志失败: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"❌ 获取 {container_name} 容器日志出错: {e}")
        return False

def main():
    """主函数"""
    print("Magentic-UI 远程服务日志查看")
    print("=" * 60)
    
    # 检查 Docker API 连接
    if not check_docker_api():
        print("❌ 无法连接到远程 Docker API，请检查配置")
        return 1
    
    # 获取命令行参数
    tail_lines = 50
    if len(sys.argv) > 1:
        try:
            tail_lines = int(sys.argv[1])
        except ValueError:
            print("⚠️  参数无效，使用默认值 50 行日志")
    
    # 获取 Magentic-UI 容器日志
    print("\n" + "=" * 50)
    print("获取 Magentic-UI 容器日志")
    print("=" * 50)
    get_container_logs("magentic-ui", tail_lines)
    
    # 获取浏览器容器日志
    print("\n" + "=" * 50)
    print("获取浏览器容器日志")
    print("=" * 50)
    get_container_logs("magentic-ui-browser", tail_lines)
    
    print("\n" + "=" * 60)
    print("日志获取完成!")
    print("\n提示:")
    print("- 要获取更多日志行数，可以使用: python view_remote_logs.py <行数>")
    print("- 例如: python view_remote_logs.py 100  (获取最近100行日志)")
    
    return 0

if __name__ == "__main__":
    exit(main())
