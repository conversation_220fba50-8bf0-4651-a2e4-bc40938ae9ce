#!/usr/bin/env python3
"""
ModelScope API 性能监控脚本
用于监控 API 调用的性能指标
"""

import asyncio
import time
import json
import statistics
from datetime import datetime
from typing import List, Dict, Any
from openai import OpenAI
from dotenv import load_dotenv
import os

load_dotenv()

class APIPerformanceMonitor:
    def __init__(self):
        self.client = OpenAI(
            base_url=os.getenv('OPENAI_BASE_URL', 'https://api-inference.modelscope.cn/v1'),
            api_key=os.getenv('OPENAI_API_KEY'),
        )
        self.model = os.getenv('DEFAULT_MODEL', 'Qwen/Qwen3-Coder-480B-A35B-Instruct')
        self.results = []

    def test_single_request(self, prompt: str, max_tokens: int = 100) -> Dict[str, Any]:
        """测试单个请求的性能"""
        start_time = time.time()
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {'role': 'system', 'content': 'You are a helpful assistant.'},
                    {'role': 'user', 'content': prompt}
                ],
                max_tokens=max_tokens,
                stream=False
            )
            
            end_time = time.time()
            latency = end_time - start_time
            
            return {
                'success': True,
                'latency': latency,
                'prompt_tokens': response.usage.prompt_tokens,
                'completion_tokens': response.usage.completion_tokens,
                'total_tokens': response.usage.total_tokens,
                'response_length': len(response.choices[0].message.content),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            end_time = time.time()
            return {
                'success': False,
                'latency': end_time - start_time,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def test_streaming_request(self, prompt: str, max_tokens: int = 100) -> Dict[str, Any]:
        """测试流式请求的性能"""
        start_time = time.time()
        first_token_time = None
        total_tokens = 0
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {'role': 'system', 'content': 'You are a helpful assistant.'},
                    {'role': 'user', 'content': prompt}
                ],
                max_tokens=max_tokens,
                stream=True
            )
            
            full_response = ""
            for chunk in response:
                if chunk.choices[0].delta.content:
                    if first_token_time is None:
                        first_token_time = time.time()
                    full_response += chunk.choices[0].delta.content
                    total_tokens += 1
            
            end_time = time.time()
            total_latency = end_time - start_time
            first_token_latency = first_token_time - start_time if first_token_time else total_latency
            
            return {
                'success': True,
                'total_latency': total_latency,
                'first_token_latency': first_token_latency,
                'tokens_per_second': total_tokens / total_latency if total_latency > 0 else 0,
                'response_length': len(full_response),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            end_time = time.time()
            return {
                'success': False,
                'total_latency': end_time - start_time,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def run_performance_test(self, num_requests: int = 10) -> Dict[str, Any]:
        """运行性能测试"""
        print(f"开始性能测试 - {num_requests} 个请求")
        print("=" * 50)
        
        test_prompts = [
            "你好，请简单介绍一下你自己",
            "请写一个Python函数来计算斐波那契数列",
            "解释一下什么是机器学习",
            "请帮我写一个简单的排序算法",
            "什么是人工智能的发展历史？"
        ]
        
        # 非流式请求测试
        print("测试非流式请求...")
        non_streaming_results = []
        for i in range(num_requests):
            prompt = test_prompts[i % len(test_prompts)]
            result = self.test_single_request(prompt)
            non_streaming_results.append(result)
            print(f"请求 {i+1}/{num_requests}: {'✅' if result['success'] else '❌'} "
                  f"延迟: {result.get('latency', 0):.2f}s")
        
        # 流式请求测试
        print("\n测试流式请求...")
        streaming_results = []
        for i in range(min(5, num_requests)):  # 流式测试较少次数
            prompt = test_prompts[i % len(test_prompts)]
            result = self.test_streaming_request(prompt)
            streaming_results.append(result)
            print(f"流式请求 {i+1}: {'✅' if result['success'] else '❌'} "
                  f"总延迟: {result.get('total_latency', 0):.2f}s "
                  f"首token: {result.get('first_token_latency', 0):.2f}s")
        
        return self.analyze_results(non_streaming_results, streaming_results)

    def analyze_results(self, non_streaming: List[Dict], streaming: List[Dict]) -> Dict[str, Any]:
        """分析测试结果"""
        successful_non_streaming = [r for r in non_streaming if r['success']]
        successful_streaming = [r for r in streaming if r['success']]
        
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'model': self.model,
            'non_streaming': {
                'total_requests': len(non_streaming),
                'successful_requests': len(successful_non_streaming),
                'success_rate': len(successful_non_streaming) / len(non_streaming) * 100,
            },
            'streaming': {
                'total_requests': len(streaming),
                'successful_requests': len(successful_streaming),
                'success_rate': len(successful_streaming) / len(streaming) * 100,
            }
        }
        
        if successful_non_streaming:
            latencies = [r['latency'] for r in successful_non_streaming]
            token_counts = [r['total_tokens'] for r in successful_non_streaming]
            
            analysis['non_streaming'].update({
                'avg_latency': statistics.mean(latencies),
                'median_latency': statistics.median(latencies),
                'min_latency': min(latencies),
                'max_latency': max(latencies),
                'avg_tokens': statistics.mean(token_counts),
                'tokens_per_second': statistics.mean([t/l for t, l in zip(token_counts, latencies)])
            })
        
        if successful_streaming:
            total_latencies = [r['total_latency'] for r in successful_streaming]
            first_token_latencies = [r['first_token_latency'] for r in successful_streaming]
            tokens_per_sec = [r['tokens_per_second'] for r in successful_streaming]
            
            analysis['streaming'].update({
                'avg_total_latency': statistics.mean(total_latencies),
                'avg_first_token_latency': statistics.mean(first_token_latencies),
                'avg_tokens_per_second': statistics.mean(tokens_per_sec),
                'max_tokens_per_second': max(tokens_per_sec)
            })
        
        return analysis

    def print_analysis(self, analysis: Dict[str, Any]):
        """打印分析结果"""
        print("\n" + "=" * 60)
        print("性能测试分析结果")
        print("=" * 60)
        
        print(f"测试时间: {analysis['timestamp']}")
        print(f"测试模型: {analysis['model']}")
        
        # 非流式结果
        ns = analysis['non_streaming']
        print(f"\n📊 非流式请求结果:")
        print(f"  总请求数: {ns['total_requests']}")
        print(f"  成功请求数: {ns['successful_requests']}")
        print(f"  成功率: {ns['success_rate']:.1f}%")
        
        if 'avg_latency' in ns:
            print(f"  平均延迟: {ns['avg_latency']:.2f}s")
            print(f"  中位数延迟: {ns['median_latency']:.2f}s")
            print(f"  最小延迟: {ns['min_latency']:.2f}s")
            print(f"  最大延迟: {ns['max_latency']:.2f}s")
            print(f"  平均token数: {ns['avg_tokens']:.0f}")
            print(f"  平均处理速度: {ns['tokens_per_second']:.1f} tokens/s")
        
        # 流式结果
        s = analysis['streaming']
        print(f"\n🚀 流式请求结果:")
        print(f"  总请求数: {s['total_requests']}")
        print(f"  成功请求数: {s['successful_requests']}")
        print(f"  成功率: {s['success_rate']:.1f}%")
        
        if 'avg_total_latency' in s:
            print(f"  平均总延迟: {s['avg_total_latency']:.2f}s")
            print(f"  平均首token延迟: {s['avg_first_token_latency']:.2f}s")
            print(f"  平均生成速度: {s['avg_tokens_per_second']:.1f} tokens/s")
            print(f"  最大生成速度: {s['max_tokens_per_second']:.1f} tokens/s")

    def save_results(self, analysis: Dict[str, Any], filename: str = None):
        """保存结果到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_test_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 结果已保存到: {filename}")

def main():
    """主函数"""
    monitor = APIPerformanceMonitor()
    
    print("ModelScope API 性能监控")
    print("=" * 50)
    print(f"API Base URL: {os.getenv('OPENAI_BASE_URL')}")
    print(f"Model: {monitor.model}")
    print()
    
    # 运行性能测试
    analysis = monitor.run_performance_test(num_requests=10)
    
    # 打印分析结果
    monitor.print_analysis(analysis)
    
    # 保存结果
    monitor.save_results(analysis)

if __name__ == "__main__":
    main()
