from .base_playwright_browser import Play<PERSON><PERSON><PERSON><PERSON>
from .local_playwright_browser import Local<PERSON><PERSON><PERSON><PERSON>rows<PERSON>
from .utils import get_browser_resource_config

# Docker-related imports - only import if docker is available
try:
    from .base_playwright_browser import Docker<PERSON><PERSON><PERSON><PERSON>rowser
    from .vnc_docker_playwright_browser import Vnc<PERSON>ock<PERSON><PERSON><PERSON><PERSON><PERSON>rowser
    from .headless_docker_playwright_browser import Headless<PERSON>ockerPlaywrightBrowser
    DOCKER_BROWSERS_AVAILABLE = True
except ImportError:
    # Create dummy classes when docker is not available
    DockerPlaywrightBrowser = None
    VncDockerPlaywrightBrowser = None
    HeadlessDockerPlaywrightBrowser = None
    DOCKER_BROWSERS_AVAILABLE = False

__all__ = [
    "PlaywrightBrowser",
    "LocalPlaywrightBrowser",
    "get_browser_resource_config",
]

# Only add Docker browsers to __all__ if they're available
if DOCKER_BROWSERS_AVAILABLE:
    __all__.extend([
        "DockerPlaywrightBrowser",
        "VncDocker<PERSON><PERSON><PERSON><PERSON>rowser",
        "Headless<PERSON>ocker<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    ])
