@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🧙‍♂️ Magentic-UI 启动脚本
echo ========================================
echo.

REM 检查虚拟环境是否存在
if not exist "magentic-env\Scripts\activate.bat" (
    echo ❌ 错误: 虚拟环境不存在
    echo 请确保 magentic-env 文件夹存在
    pause
    exit /b 1
)

echo 📦 激活虚拟环境...
call magentic-env\Scripts\activate.bat

echo.
echo 🚀 启动选项:
echo 1. 简化版服务器 (推荐，当前可用)
echo 2. 完整版服务器 (需要完整依赖)
echo 3. 退出
echo.

set /p choice="请选择启动方式 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 🌟 启动简化版服务器...
    echo 📍 访问地址: http://127.0.0.1:8081
    echo 📚 API 文档: http://127.0.0.1:8081/docs
    echo.
    echo 按 Ctrl+C 停止服务器
    echo.
    python minimal_server.py
) else if "%choice%"=="2" (
    echo.
    echo 🔧 尝试启动完整版服务器...
    python run_magentic_ui.py
) else if "%choice%"=="3" (
    echo 👋 再见!
    exit /b 0
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

echo.
echo 服务器已停止
pause
