#!/usr/bin/env python3
"""
ModelScope 配置设置脚本
用于快速配置和验证 ModelScope API 集成
"""

import os
import sys
import yaml
import shutil
from pathlib import Path
from typing import Dict, Any

def create_config_files(api_key: str, base_url: str = "https://api-inference.modelscope.cn/v1", 
                       model: str = "Qwen/Qwen3-Coder-480B-A35B-Instruct"):
    """创建配置文件"""
    
    # 创建 .env 文件
    env_content = f"""# ModelScope API Configuration
OPENAI_API_KEY={api_key}
OPENAI_BASE_URL={base_url}
DEFAULT_MODEL={model}

# Magentic-UI 特定配置
MAGENTIC_UI_DATABASE_URI=sqlite:///./magentic_ui.db
MAGENTIC_UI_API_DOCS=false
MAGENTIC_UI_CLEANUP_INTERVAL=300
MAGENTIC_UI_SESSION_TIMEOUT=360000
MAGENTIC_UI_CONFIG_DIR=configs
MAGENTIC_UI_DEFAULT_USER_ID=<EMAIL>
MAGENTIC_UI_UPGRADE_DATABASE=false

# Docker 相关配置
INSIDE_DOCKER=0
RUN_WITHOUT_DOCKER=true
MAGENTIC_UI_BROWSER_IMAGE=ghcr.io/microsoft/magentic-ui-browser:0.0.1
MAGENTIC_UI_PYTHON_IMAGE=ghcr.io/microsoft/magentic-ui-python-env:0.0.1

# 前端配置
GATSBY_API_URL=http://127.0.0.1:8081/api
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)
    
    # 创建 config.yaml 文件
    config = {
        "model_config_qwen_coder": {
            "provider": "OpenAIChatCompletionClient",
            "config": {
                "model": model,
                "base_url": base_url,
                "api_key": api_key,
                "model_info": {
                    "vision": True,
                    "function_calling": True,
                    "json_output": True,
                    "family": "qwen",
                    "structured_output": True,
                    "multiple_system_messages": True
                }
            },
            "max_retries": 10
        }
    }
    
    # 使用 YAML 引用
    yaml_content = f"""# Magentic-UI Configuration for ModelScope API
model_config_qwen_coder: &client_qwen_coder
  provider: OpenAIChatCompletionClient
  config:
    model: "{model}"
    base_url: "{base_url}"
    api_key: "{api_key}"
    model_info:
      vision: true
      function_calling: true
      json_output: true
      family: "qwen"
      structured_output: true
      multiple_system_messages: true
  max_retries: 10

# 轻量级模型配置
model_config_qwen_light: &client_qwen_light
  provider: OpenAIChatCompletionClient
  config:
    model: "Qwen/Qwen2.5-Coder-32B-Instruct"
    base_url: "{base_url}"
    api_key: "{api_key}"
    model_info:
      vision: false
      function_calling: true
      json_output: true
      family: "qwen"
      structured_output: true
      multiple_system_messages: true
  max_retries: 5

# 各个智能体的模型配置
orchestrator_client: *client_qwen_coder
coder_client: *client_qwen_coder
web_surfer_client: *client_qwen_coder
file_surfer_client: *client_qwen_coder
action_guard_client: *client_qwen_light
user_proxy_client: *client_qwen_coder
model_client: *client_qwen_coder
plan_learning_client: *client_qwen_coder
evaluation_client: *client_qwen_coder
"""
    
    with open("config.yaml", "w", encoding="utf-8") as f:
        f.write(yaml_content)
    
    print("✅ 配置文件创建成功!")
    print("   - .env")
    print("   - config.yaml")

def backup_existing_configs():
    """备份现有配置文件"""
    files_to_backup = [".env", "config.yaml"]
    
    for file in files_to_backup:
        if os.path.exists(file):
            backup_name = f"{file}.backup"
            shutil.copy2(file, backup_name)
            print(f"✅ 已备份 {file} -> {backup_name}")

def main():
    """主函数"""
    print("ModelScope API 配置设置脚本")
    print("=" * 50)
    
    # 检查是否提供了 API Key
    if len(sys.argv) < 2:
        print("使用方法: python setup_modelscope.py <API_KEY> [BASE_URL] [MODEL]")
        print("示例: python setup_modelscope.py ms-your-api-key")
        sys.exit(1)
    
    api_key = sys.argv[1]
    base_url = sys.argv[2] if len(sys.argv) > 2 else "https://api-inference.modelscope.cn/v1"
    model = sys.argv[3] if len(sys.argv) > 3 else "Qwen/Qwen3-Coder-480B-A35B-Instruct"
    
    print(f"API Key: {api_key}")
    print(f"Base URL: {base_url}")
    print(f"Model: {model}")
    print()
    
    # 备份现有配置
    backup_existing_configs()
    
    # 创建新配置
    create_config_files(api_key, base_url, model)
    
    print("\n🎉 ModelScope API 配置完成!")
    print("\n下一步:")
    print("1. 运行测试: python test_modelscope_api.py")
    print("2. 启动应用: magentic-ui --config config.yaml")

if __name__ == "__main__":
    main()
