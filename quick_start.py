#!/usr/bin/env python3
"""
Magentic-UI 快速启动脚本
一键设置环境、配置和启动应用

使用方法:
python quick_start.py --api-key ms-your-api-key-here
"""

import os
import sys
import subprocess
import argparse
import platform
from pathlib import Path
import shutil

class MagenticUIQuickStart:
    def __init__(self):
        self.system = platform.system().lower()
        self.python_cmd = self.get_python_command()
        self.pip_cmd = f"{self.python_cmd} -m pip"
        
    def get_python_command(self):
        """获取 Python 命令"""
        for cmd in ['python3', 'python']:
            try:
                result = subprocess.run([cmd, '--version'], 
                                      capture_output=True, text=True)
                if result.returncode == 0 and 'Python 3.' in result.stdout:
                    return cmd
            except FileNotFoundError:
                continue
        raise RuntimeError("未找到 Python 3.x，请先安装 Python")
    
    def print_step(self, step, message):
        """打印步骤信息"""
        print(f"\n{'='*60}")
        print(f"步骤 {step}: {message}")
        print('='*60)
    
    def run_command(self, command, description=""):
        """运行命令并处理错误"""
        print(f"执行: {command}")
        if description:
            print(f"说明: {description}")
        
        try:
            result = subprocess.run(command, shell=True, check=True, 
                                  capture_output=True, text=True)
            if result.stdout:
                print(result.stdout)
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 命令执行失败: {e}")
            if e.stderr:
                print(f"错误信息: {e.stderr}")
            return False
    
    def check_requirements(self):
        """检查系统要求"""
        self.print_step(1, "检查系统要求")
        
        # 检查 Python 版本
        result = subprocess.run([self.python_cmd, '--version'], 
                              capture_output=True, text=True)
        python_version = result.stdout.strip()
        print(f"✅ Python 版本: {python_version}")
        
        # 检查 pip
        result = subprocess.run([self.python_cmd, '-m', 'pip', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ pip 可用: {result.stdout.strip()}")
        else:
            raise RuntimeError("pip 不可用，请检查 Python 安装")
        
        # 检查网络连接
        try:
            import urllib.request
            urllib.request.urlopen('https://pypi.org', timeout=5)
            print("✅ 网络连接正常")
        except:
            print("⚠️  网络连接可能有问题，可能影响包安装")
    
    def setup_virtual_environment(self):
        """设置虚拟环境"""
        self.print_step(2, "设置虚拟环境")
        
        venv_path = Path("magentic-env")
        
        if venv_path.exists():
            print("虚拟环境已存在，跳过创建")
        else:
            if not self.run_command(f"{self.python_cmd} -m venv magentic-env", 
                                   "创建虚拟环境"):
                raise RuntimeError("虚拟环境创建失败")
            print("✅ 虚拟环境创建成功")
        
        # 获取激活命令
        if self.system == "windows":
            self.activate_cmd = "magentic-env\\Scripts\\activate"
            self.python_venv = "magentic-env\\Scripts\\python"
            self.pip_venv = "magentic-env\\Scripts\\pip"
        else:
            self.activate_cmd = "source magentic-env/bin/activate"
            self.python_venv = "magentic-env/bin/python"
            self.pip_venv = "magentic-env/bin/pip"
        
        print(f"虚拟环境激活命令: {self.activate_cmd}")
    
    def install_dependencies(self):
        """安装依赖包"""
        self.print_step(3, "安装依赖包")
        
        # 升级 pip
        self.run_command(f"{self.pip_venv} install --upgrade pip", "升级 pip")
        
        # 基础依赖
        basic_packages = [
            "pyyaml",
            "python-dotenv", 
            "openai",
            "tiktoken",
            "fastapi[standard]",
            "uvicorn",
            "aiofiles",
            "sqlmodel",
            "playwright",
        ]
        
        print("安装基础依赖包...")
        for package in basic_packages:
            if not self.run_command(f"{self.pip_venv} install {package}"):
                print(f"⚠️  {package} 安装失败，继续安装其他包")
        
        # AutoGen 相关包
        autogen_packages = [
            "autogen-agentchat",
            "autogen-core", 
            "autogen-ext"
        ]
        
        print("安装 AutoGen 相关包...")
        for package in autogen_packages:
            if not self.run_command(f"{self.pip_venv} install {package}"):
                print(f"⚠️  {package} 安装失败，继续安装其他包")
        
        # 安装 Playwright 浏览器
        print("安装 Playwright 浏览器...")
        self.run_command(f"{self.python_venv} -m playwright install", 
                        "安装浏览器驱动")
        
        print("✅ 依赖包安装完成")
    
    def create_config_files(self, api_key, base_url, model):
        """创建配置文件"""
        self.print_step(4, "创建配置文件")
        
        # 创建 .env 文件
        env_content = f"""# ModelScope API 配置
OPENAI_API_KEY={api_key}
OPENAI_BASE_URL={base_url}
DEFAULT_MODEL={model}

# Magentic-UI 配置
MAGENTIC_UI_DATABASE_URI=sqlite:///./magentic_ui.db
MAGENTIC_UI_API_DOCS=true
MAGENTIC_UI_DEFAULT_USER_ID=<EMAIL>
MAGENTIC_UI_CLEANUP_INTERVAL=300
MAGENTIC_UI_SESSION_TIMEOUT=360000
MAGENTIC_UI_CONFIG_DIR=configs

# Docker 配置
INSIDE_DOCKER=0
RUN_WITHOUT_DOCKER=true

# 前端配置
GATSBY_API_URL=http://127.0.0.1:8081/api
"""
        
        with open(".env", "w", encoding="utf-8") as f:
            f.write(env_content)
        print("✅ .env 文件创建成功")
        
        # 创建 config.yaml 文件
        config_content = f"""# Magentic-UI 配置文件
model_config_main: &main_model
  provider: OpenAIChatCompletionClient
  config:
    model: "{model}"
    base_url: "{base_url}"
    api_key: "{api_key}"
    model_info:
      vision: true
      function_calling: true
      json_output: true
      family: "qwen"
      structured_output: true
  max_retries: 5

model_config_light: &light_model
  provider: OpenAIChatCompletionClient
  config:
    model: "Qwen/Qwen2.5-Coder-32B-Instruct"
    base_url: "{base_url}"
    api_key: "{api_key}"
    model_info:
      vision: false
      function_calling: true
      json_output: true
      family: "qwen"
  max_retries: 3

# 智能体配置
orchestrator_client: *main_model
coder_client: *main_model
web_surfer_client: *main_model
file_surfer_client: *main_model
action_guard_client: *light_model
user_proxy_client: *main_model
model_client: *main_model
"""
        
        with open("config.yaml", "w", encoding="utf-8") as f:
            f.write(config_content)
        print("✅ config.yaml 文件创建成功")
    
    def test_configuration(self):
        """测试配置"""
        self.print_step(5, "测试配置")
        
        if Path("test_modelscope_api.py").exists():
            print("运行配置测试...")
            if self.run_command(f"{self.python_venv} test_modelscope_api.py"):
                print("✅ 配置测试通过")
            else:
                print("⚠️  配置测试失败，请检查 API Key 和网络连接")
        else:
            print("测试文件不存在，跳过配置测试")
    
    def create_startup_script(self):
        """创建启动脚本"""
        self.print_step(6, "创建启动脚本")
        
        if self.system == "windows":
            script_content = f"""@echo off
echo 启动 Magentic-UI...
call {self.activate_cmd}
python -m magentic_ui.backend.cli --config config.yaml --host 127.0.0.1 --port 8081
pause
"""
            script_name = "start_magentic_ui.bat"
        else:
            script_content = f"""#!/bin/bash
echo "启动 Magentic-UI..."
{self.activate_cmd}
python -m magentic_ui.backend.cli --config config.yaml --host 127.0.0.1 --port 8081
"""
            script_name = "start_magentic_ui.sh"
        
        with open(script_name, "w", encoding="utf-8") as f:
            f.write(script_content)
        
        if self.system != "windows":
            os.chmod(script_name, 0o755)
        
        print(f"✅ 启动脚本创建成功: {script_name}")
    
    def print_success_message(self):
        """打印成功信息"""
        print("\n" + "🎉" * 20)
        print("Magentic-UI 快速启动完成！")
        print("🎉" * 20)
        
        print("\n📋 下一步操作:")
        print("1. 激活虚拟环境:")
        print(f"   {self.activate_cmd}")
        
        print("\n2. 启动应用:")
        if self.system == "windows":
            print("   双击 start_magentic_ui.bat")
            print("   或运行: start_magentic_ui.bat")
        else:
            print("   ./start_magentic_ui.sh")
        
        print("\n3. 访问 Web 界面:")
        print("   http://127.0.0.1:8081")
        
        print("\n📚 学习资源:")
        print("   - 新手指南: MAGENTIC_UI_BEGINNER_GUIDE.md")
        print("   - 实战教程: PRACTICAL_TUTORIALS.md")
        print("   - 配置说明: MODELSCOPE_SETUP.md")
        
        print("\n🆘 获得帮助:")
        print("   - GitHub Issues: https://github.com/microsoft/magentic-ui/issues")
        print("   - 社区讨论: https://github.com/microsoft/magentic-ui/discussions")

def main():
    parser = argparse.ArgumentParser(description="Magentic-UI 快速启动脚本")
    parser.add_argument("--api-key", required=True, 
                       help="ModelScope API Key (格式: ms-xxxxxxxxxx)")
    parser.add_argument("--base-url", 
                       default="https://api-inference.modelscope.cn/v1",
                       help="API Base URL")
    parser.add_argument("--model", 
                       default="Qwen/Qwen3-Coder-480B-A35B-Instruct",
                       help="默认模型名称")
    parser.add_argument("--skip-test", action="store_true",
                       help="跳过配置测试")
    
    args = parser.parse_args()
    
    print("🚀 Magentic-UI 快速启动脚本")
    print("=" * 60)
    
    try:
        starter = MagenticUIQuickStart()
        
        # 执行安装步骤
        starter.check_requirements()
        starter.setup_virtual_environment()
        starter.install_dependencies()
        starter.create_config_files(args.api_key, args.base_url, args.model)
        
        if not args.skip_test:
            starter.test_configuration()
        
        starter.create_startup_script()
        starter.print_success_message()
        
    except Exception as e:
        print(f"\n❌ 安装过程中出现错误: {e}")
        print("\n🔧 故障排除建议:")
        print("1. 检查网络连接")
        print("2. 确认 Python 版本 >= 3.10")
        print("3. 验证 API Key 是否正确")
        print("4. 查看详细错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
