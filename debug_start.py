#!/usr/bin/env python3
"""
调试 Magentic-UI 启动问题
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def debug_start():
    """调试启动过程"""
    print("调试 Magentic-UI 启动过程")
    print("=" * 50)
    
    # 检查环境
    print("1. 环境检查:")
    print(f"   当前目录: {os.getcwd()}")
    print(f"   Python 路径: {sys.executable}")
    
    # 检查虚拟环境
    venv_python = Path("magentic-env/Scripts/python.exe")
    if venv_python.exists():
        print(f"   虚拟环境 Python: {venv_python.absolute()}")
    else:
        print("   ❌ 虚拟环境 Python 不存在")
        return False
    
    # 检查配置文件
    print("\n2. 配置文件检查:")
    config_files = [".env", "config.yaml"]
    for file in config_files:
        if Path(file).exists():
            print(f"   ✅ {file} 存在")
        else:
            print(f"   ❌ {file} 不存在")
    
    # 尝试导入 magentic_ui
    print("\n3. 模块导入测试:")
    try:
        cmd = [str(venv_python), "-c", "import magentic_ui; print('导入成功')"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("   ✅ magentic_ui 模块导入成功")
        else:
            print("   ❌ magentic_ui 模块导入失败")
            print(f"   错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("   ❌ 模块导入超时")
        return False
    except Exception as e:
        print(f"   ❌ 模块导入异常: {e}")
        return False
    
    # 尝试启动 CLI
    print("\n4. CLI 启动测试:")
    try:
        cmd = [
            str(venv_python), 
            "-m", "magentic_ui.backend.cli",
            "--help"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("   ✅ CLI 帮助信息获取成功")
            print("   CLI 可用选项:")
            for line in result.stdout.split('\n')[:10]:  # 只显示前10行
                if line.strip():
                    print(f"     {line}")
        else:
            print("   ❌ CLI 启动失败")
            print(f"   错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("   ❌ CLI 启动超时")
        return False
    except Exception as e:
        print(f"   ❌ CLI 启动异常: {e}")
        return False
    
    # 尝试启动服务（短时间）
    print("\n5. 服务启动测试:")
    try:
        cmd = [
            str(venv_python), 
            "-m", "magentic_ui.backend.cli",
            "--config", "config.yaml",
            "--host", "127.0.0.1",
            "--port", "8081"
        ]
        
        print(f"   启动命令: {' '.join(cmd)}")
        
        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        print(f"   ✅ 进程已启动 (PID: {process.pid})")
        
        # 等待5秒看看输出
        time.sleep(5)
        
        # 检查进程状态
        poll_result = process.poll()
        if poll_result is None:
            print("   ✅ 进程仍在运行")
            
            # 尝试读取一些输出
            try:
                stdout, stderr = process.communicate(timeout=2)
                if stdout:
                    print("   标准输出:")
                    for line in stdout.split('\n')[:10]:
                        if line.strip():
                            print(f"     {line}")
                if stderr:
                    print("   错误输出:")
                    for line in stderr.split('\n')[:10]:
                        if line.strip():
                            print(f"     {line}")
            except subprocess.TimeoutExpired:
                print("   进程仍在运行，无法获取完整输出")
            
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=5)
                print("   ✅ 进程已正常终止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("   ⚠️  进程被强制终止")
            
            return True
            
        else:
            print(f"   ❌ 进程已退出 (返回码: {poll_result})")
            stdout, stderr = process.communicate()
            if stdout:
                print("   标准输出:")
                print(f"     {stdout}")
            if stderr:
                print("   错误输出:")
                print(f"     {stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ 服务启动异常: {e}")
        return False
    
    return True

def main():
    """主函数"""
    success = debug_start()
    
    print("\n" + "=" * 50)
    print("调试总结")
    print("=" * 50)
    
    if success:
        print("🎉 基础启动测试通过！")
        print("✅ Magentic-UI 可以启动，但可能需要更长时间完全加载")
        print("\n💡 建议:")
        print("1. 手动运行启动脚本并等待更长时间")
        print("2. 检查防火墙设置")
        print("3. 确保端口 8081 未被占用")
    else:
        print("❌ 启动过程中发现问题")
        print("需要进一步排查和修复")
    
    return success

if __name__ == "__main__":
    main()
