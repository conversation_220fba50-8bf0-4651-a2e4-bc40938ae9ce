version: '3.8'

services:
  magentic-ui:
    image: ghcr.io/microsoft/magentic-ui-python-env:0.0.1
    container_name: magentic-ui
    env_file:
      - .env.remote
    environment:
      # 远程 Docker 配置
      - DOCKER_HOST=tcp://************:2375
      - _HOST=0.0.0.0
      - _PORT=8081
      - _CONFIG=/workspace/config.yaml
      - PYTHONPATH=/opt/magentic-ui/src
      - MAGENTIC_UI_API_DOCS=true
    ports:
      - "8081:8081"
    volumes:
      - ./config.yaml:/workspace/config.yaml
      - ./data:/workspace/data
      - ./temp_install:/install
    working_dir: /opt/magentic-ui
    command: [
      "sh", "-c", "pip install /install/magent_ui-0.1.17-py3-none-any.whl && python run_magentic_ui.py"
    ]
    
  magentic-browser:
    image: ghcr.io/microsoft/magentic-ui-browser:0.0.1
    container_name: magentic-ui-browser
    env_file:
      - .env.remote
    environment:
      - PLAYWRIGHT_WS_PATH=default
      - DISPLAY=:99
      - NO_VNC_PORT=6080
    ports:
      - "6080:6080"
      - "37367:37367"
    volumes:
      - ./data:/workspace/data
