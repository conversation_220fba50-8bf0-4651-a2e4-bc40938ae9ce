#!/usr/bin/env python3
"""
使用 Docker API 拉取远程 Magentic-UI 所需镜像
"""

import requests
import json
import time

# 远程 Docker API 地址
DOCKER_API_URL = "http://8.137.154.11:2375"

# 需要拉取的镜像列表
REQUIRED_IMAGES = [
    "ghcr.io/microsoft/magentic-ui-python-env:0.0.1",
    "ghcr.io/microsoft/magentic-ui-browser:0.0.1"
]

def check_docker_api():
    """检查 Docker API 是否可用"""
    try:
        response = requests.get(f"{DOCKER_API_URL}/version", timeout=10)
        if response.status_code == 200:
            version_info = response.json()
            print("✅ Docker API 连接成功!")
            print(f"Docker 版本: {version_info.get('Version', '未知')}")
            print(f"API 版本: {version_info.get('ApiVersion', '未知')}")
            return True
        else:
            print(f"❌ Docker API 连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Docker API 连接出错: {e}")
        return False

def pull_image(image_name):
    """拉取单个镜像"""
    try:
        # 分离镜像名称和标签
        if ':' in image_name:
            repo, tag = image_name.rsplit(':', 1)
        else:
            repo, tag = image_name, 'latest'
        
        print(f"\n正在拉取镜像: {image_name}")
        print("这可能需要几分钟时间，请耐心等待...")
        
        # 发送拉取镜像的请求
        params = {
            'fromImage': repo,
            'tag': tag
        }
        
        response = requests.post(
            f"{DOCKER_API_URL}/images/create",
            params=params,
            timeout=300  # 5分钟超时
        )
        
        if response.status_code == 200:
            print(f"✅ 镜像 {image_name} 拉取成功!")
            return True
        else:
            print(f"❌ 拉取镜像 {image_name} 失败: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"❌ 拉取镜像 {image_name} 出错: {e}")
        return False

def list_images():
    """列出所有镜像"""
    try:
        response = requests.get(f"{DOCKER_API_URL}/images/json", timeout=30)
        if response.status_code == 200:
            images = response.json()
            print(f"\n服务器上共有 {len(images)} 个镜像:")
            for image in images:
                tags = image.get('RepoTags', ['<none>'])
                if tags:
                    for tag in tags:
                        print(f"  - {tag}")
                else:
                    print(f"  - {image.get('Id', 'N/A')[:12]} (无标签)")
            return True
        else:
            print(f"❌ 获取镜像列表失败: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"❌ 获取镜像列表出错: {e}")
        return False

def main():
    """主函数"""
    print("Magentic-UI 远程镜像拉取脚本")
    print("=" * 60)
    
    # 检查 Docker API 连接
    if not check_docker_api():
        print("❌ 无法连接到远程 Docker API，请检查配置")
        return 1
    
    # 拉取所需镜像
    print("\n" + "=" * 50)
    print("开始拉取 Magentic-UI 所需镜像")
    print("=" * 50)
    
    success_count = 0
    for image in REQUIRED_IMAGES:
        if pull_image(image):
            success_count += 1
        else:
            print(f"⚠️  镜像 {image} 拉取失败")
    
    # 显示结果
    print("\n" + "=" * 60)
    print(f"镜像拉取完成: {success_count}/{len(REQUIRED_IMAGES)} 个成功")
    
    if success_count == len(REQUIRED_IMAGES):
        print("🎉 所有镜像拉取成功!")
        print("\n当前服务器上的镜像:")
        list_images()
        print("\n现在可以使用以下命令启动服务:")
        print("- python start_remote_services.py")
        print("- 或使用批处理脚本: start_magentic_ui_remote.bat")
    else:
        print("⚠️  部分镜像拉取失败，请检查网络连接或镜像名称")
        print("\n当前服务器上的镜像:")
        list_images()
    
    return 0

if __name__ == "__main__":
    exit(main())
