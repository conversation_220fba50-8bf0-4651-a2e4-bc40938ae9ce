#!/usr/bin/env python3
"""
简单的 Magentic-UI 启动测试
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_config_loading():
    """测试配置加载"""
    print("测试配置加载...")
    
    try:
        import yaml
        from dotenv import load_dotenv
        
        # 加载环境变量
        load_dotenv()
        
        # 检查关键环境变量
        api_key = os.getenv("OPENAI_API_KEY")
        base_url = os.getenv("OPENAI_BASE_URL")
        
        if not api_key:
            print("❌ OPENAI_API_KEY 未设置")
            return False
        
        if not base_url:
            print("❌ OPENAI_BASE_URL 未设置")
            return False
        
        print(f"✅ API Key: {api_key[:10]}...")
        print(f"✅ Base URL: {base_url}")
        
        # 测试 config.yaml 加载
        if Path("config.yaml").exists():
            with open("config.yaml", "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
            print("✅ config.yaml 加载成功")
            return True
        else:
            print("❌ config.yaml 不存在")
            return False
            
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def test_api_connection():
    """测试 API 连接"""
    print("\n测试 API 连接...")
    
    try:
        from openai import OpenAI
        
        client = OpenAI(
            base_url=os.getenv("OPENAI_BASE_URL"),
            api_key=os.getenv("OPENAI_API_KEY"),
        )
        
        # 简单的 API 调用测试
        response = client.chat.completions.create(
            model=os.getenv("DEFAULT_MODEL", "Qwen/Qwen3-Coder-480B-A35B-Instruct"),
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=10
        )
        
        print("✅ API 连接成功")
        print(f"✅ 响应: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ API 连接失败: {e}")
        return False

def test_autogen_basic():
    """测试 AutoGen 基础功能"""
    print("\n测试 AutoGen 基础功能...")
    
    try:
        from autogen_core.models import ChatCompletionClient, UserMessage
        
        # 创建客户端配置
        config = {
            "provider": "OpenAIChatCompletionClient",
            "config": {
                "model": os.getenv("DEFAULT_MODEL", "Qwen/Qwen3-Coder-480B-A35B-Instruct"),
                "base_url": os.getenv("OPENAI_BASE_URL"),
                "api_key": os.getenv("OPENAI_API_KEY"),
            },
            "max_retries": 3
        }
        
        print("✅ AutoGen 配置创建成功")
        return True
        
    except Exception as e:
        print(f"❌ AutoGen 测试失败: {e}")
        return False

async def test_autogen_async():
    """异步测试 AutoGen"""
    print("\n异步测试 AutoGen...")
    
    try:
        from autogen_core.models import ChatCompletionClient, UserMessage
        
        config = {
            "provider": "OpenAIChatCompletionClient",
            "config": {
                "model": os.getenv("DEFAULT_MODEL", "Qwen/Qwen3-Coder-480B-A35B-Instruct"),
                "base_url": os.getenv("OPENAI_BASE_URL"),
                "api_key": os.getenv("OPENAI_API_KEY"),
                "model_info": {
                    "vision": True,
                    "function_calling": True,
                    "json_output": True,
                    "family": "qwen",
                    "structured_output": True
                }
            },
            "max_retries": 3
        }
        
        client = ChatCompletionClient.load_component(config)
        
        response = await client.create(
            messages=[UserMessage(content="Hello", source="user")]
        )
        
        print("✅ AutoGen 异步调用成功")
        print(f"✅ 响应: {response.content[:50]}...")
        
        await client.close()
        return True
        
    except Exception as e:
        print(f"❌ AutoGen 异步测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Magentic-UI 简单启动测试")
    print("=" * 50)
    
    results = []
    
    # 测试配置加载
    results.append(test_config_loading())
    
    # 测试 API 连接
    results.append(test_api_connection())
    
    # 测试 AutoGen 基础
    results.append(test_autogen_basic())
    
    # 异步测试 AutoGen
    try:
        result = asyncio.run(test_autogen_async())
        results.append(result)
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")
        results.append(False)
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"通过测试: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有基础测试通过！")
        print("✅ Magentic-UI 基础环境配置正确")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
