#!/usr/bin/env python3
"""
Magentic-UI 启动脚本
修复了依赖问题，可以在没有完整依赖的情况下启动
"""

import sys
import os
import asyncio
from pathlib import Path

def setup_environment():
    """设置环境变量和路径"""
    # 添加 src 目录到 Python 路径
    src_path = Path(__file__).parent / "src"
    sys.path.insert(0, str(src_path))
    
    # 设置环境变量
    os.environ.update({
        "PYTHONPATH": str(src_path),
        "_HOST": "127.0.0.1",
        "_PORT": "8081",
        "_CONFIG": "config.yaml",
        "RUN_WITHOUT_DOCKER": "true",
        "_API_DOCS": "true",
        "_APPDIR": str(Path.home() / ".magentic_ui"),
        "INSIDE_DOCKER": "0",
        "EXTERNAL_WORKSPACE_ROOT": str(Path.home() / ".magentic_ui"),
        "INTERNAL_WORKSPACE_ROOT": str(Path.home() / ".magentic_ui"),
    })

def main():
    """主函数"""
    setup_environment()
    
    print("=" * 50)
    print("启动 Magentic-UI")
    print("=" * 50)
    print(f"主机: 127.0.0.1")
    print(f"端口: 8081")
    print(f"配置文件: config.yaml")
    print(f"运行模式: 无 Docker (简化模式)")
    print("=" * 50)
    print()
    
    try:
        # 尝试导入并运行 CLI
        print("正在启动应用...")
        from magentic_ui.backend.cli import run_ui
        
        run_ui(
            host="127.0.0.1",
            port=8081,
            workers=1,
            reload=False,
            docs=True,
            appdir=str(Path.home() / ".magentic_ui"),
            database_uri=None,
            upgrade_database=False,
            config="config.yaml",
            run_without_docker=True
        )
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("尝试直接使用 uvicorn 启动...")
        
        try:
            import uvicorn
            uvicorn.run(
                "magentic_ui.backend.web.app:app",
                host="127.0.0.1",
                port=8081,
                reload=False,
                log_level="info"
            )
        except Exception as e2:
            print(f"启动失败: {e2}")
            print("\n可能的解决方案:")
            print("1. 确保已激活虚拟环境: magentic-env\\Scripts\\activate")
            print("2. 安装缺失的依赖: pip install tldextract playwright")
            print("3. 或者运行: pip install -e .")
            return 1
    
    except Exception as e:
        print(f"运行时错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
