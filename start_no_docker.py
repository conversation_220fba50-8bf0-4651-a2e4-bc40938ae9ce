#!/usr/bin/env python3
"""
Magentic-UI 无 Docker 启动脚本
专为不支持虚拟化的硬件环境设计
"""

import sys
import os
import asyncio
from pathlib import Path

def setup_environment():
    """设置环境变量和路径"""
    # 添加 src 目录到 Python 路径
    src_path = Path(__file__).parent / "src"
    sys.path.insert(0, str(src_path))
    
    # 设置环境变量 - 强制禁用所有 Docker 功能
    os.environ.update({
        "PYTHONPATH": str(src_path),
        "_HOST": "127.0.0.1",
        "_PORT": "8081",
        "_CONFIG": "config.yaml",
        "RUN_WITHOUT_DOCKER": "true",
        "_API_DOCS": "true",
        "_APPDIR": str(Path.home() / ".magentic_ui"),
        "INSIDE_DOCKER": "false",
        "EXTERNAL_WORKSPACE_ROOT": str(Path.home() / ".magentic_ui"),
        "INTERNAL_WORKSPACE_ROOT": str(Path.home() / ".magentic_ui"),
        # 禁用 Docker 相关功能
        "DISABLE_DOCKER": "true",
        "NO_DOCKER": "true",
        "DOCKER_AVAILABLE": "false",
        # 强制使用本地浏览器
        "FORCE_LOCAL_BROWSER": "true",
        "PLAYWRIGHT_BROWSER_TYPE": "local",
    })

def patch_docker_imports():
    """在导入前修补 Docker 相关模块"""
    import sys
    
    # 创建虚拟的 docker 模块
    class MockDocker:
        class errors:
            class DockerException(Exception):
                pass
            class ImageNotFound(Exception):
                pass

        class models:
            class containers:
                Container = None

        class types:
            # Mock docker types
            pass

        def from_env(self):
            raise ImportError("Docker not available")

    # 将虚拟模块注入到 sys.modules
    sys.modules['docker'] = MockDocker()
    sys.modules['docker.errors'] = MockDocker.errors
    sys.modules['docker.models'] = MockDocker.models
    sys.modules['docker.models.containers'] = MockDocker.models.containers
    sys.modules['docker.types'] = MockDocker.types

def main():
    """主函数"""
    setup_environment()
    patch_docker_imports()
    
    print("=" * 60)
    print("🧙‍♂️ Magentic-UI (无 Docker 版本)")
    print("=" * 60)
    print(f"🌐 主机: 127.0.0.1")
    print(f"🔌 端口: 8081")
    print(f"⚙️  配置文件: config.yaml")
    print(f"🐳 Docker: 已禁用 (硬件不支持)")
    print(f"🌐 浏览器: 本地 Playwright")
    print("=" * 60)
    print()
    
    try:
        print("正在启动应用...")
        
        # 尝试导入并运行 CLI
        from magentic_ui.backend.cli import run_ui
        
        print("✅ 成功导入 CLI 模块")
        
        run_ui(
            host="127.0.0.1",
            port=8081,
            workers=1,
            reload=False,
            docs=True,
            appdir=str(Path.home() / ".magentic_ui"),
            database_uri=None,
            upgrade_database=False,
            config="config.yaml",
            run_without_docker=True
        )
        
    except ImportError as e:
        print(f"⚠️  导入错误: {e}")
        print("尝试直接使用 uvicorn 启动...")
        
        try:
            import uvicorn
            print("✅ 使用 uvicorn 直接启动")
            
            uvicorn.run(
                "magentic_ui.backend.web.app:app",
                host="127.0.0.1",
                port=8081,
                reload=False,
                log_level="info"
            )
        except Exception as e2:
            print(f"❌ 启动失败: {e2}")
            print("\n🔧 故障排除:")
            print("1. 确保已激活虚拟环境")
            print("2. 确保已安装 playwright: pip install playwright")
            print("3. 运行 playwright install 安装浏览器")
            return 1
    
    except Exception as e:
        print(f"❌ 运行时错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
