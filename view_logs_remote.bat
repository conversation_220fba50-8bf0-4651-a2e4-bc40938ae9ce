@echo off
setlocal

echo ========================================
echo 查看 Magentic-UI 日志 (远程 Docker 模式)
echo ========================================

REM 设置环境变量文件
echo 加载环境变量文件: .env.remote

REM 检查环境变量文件是否存在
if not exist ".env.remote" (
    echo ⚠️  环境变量文件 .env.remote 不存在!
    echo 尝试使用默认的 Docker 主机设置...
    set "DOCKER_HOST=tcp://************:2375"
) else (
    REM 加载环境变量
    for /f "usebackq tokens=*" %%a in (`.env.remote`) do (
        set "line=%%a"
        if not "!line:~0,1!"=="#" if "!line!" neq "" (
            set "!line!"
        )
    )
)

echo 当前 DOCKER_HOST=%DOCKER_HOST%

REM 检查 Docker 连接
echo.
echo 正在检查 Docker 连接...
docker version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Docker 连接正常
) else (
    echo ⚠️  Docker 连接失败，但仍尝试查看日志...
)

REM 检查 docker-compose 文件是否存在
if not exist "docker-compose.remote.yml" (
    echo ❌ docker-compose.remote.yml 文件不存在!
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

REM 显示可用的服务
echo.
echo 可用的服务:
docker-compose -f docker-compose.remote.yml config --services

REM 询问用户要查看哪个服务的日志
echo.
echo 请选择要查看日志的服务:
echo 1. magentic-ui-app (主应用)
echo 2. magentic-ui-browser (浏览器)
echo 3. 所有服务
echo 4. 退出

set /p SERVICE_CHOICE=请输入选项 (1-4): 

REM 根据用户选择查看日志
if "%SERVICE_CHOICE%"=="1" (
    echo.
    echo 正在查看 magentic-ui-app 服务日志...
    echo 使用配置文件: docker-compose.remote.yml
    echo.
    docker-compose -f docker-compose.remote.yml logs -f magentic-ui-app
) else if "%SERVICE_CHOICE%"=="2" (
    echo.
    echo 正在查看 magentic-ui-browser 服务日志...
    echo 使用配置文件: docker-compose.remote.yml
    echo.
    docker-compose -f docker-compose.remote.yml logs -f magentic-ui-browser
) else if "%SERVICE_CHOICE%"=="3" (
    echo.
    echo 正在查看所有服务日志...
    echo 使用配置文件: docker-compose.remote.yml
    echo.
    docker-compose -f docker-compose.remote.yml logs -f
) else if "%SERVICE_CHOICE%"=="4" (
    echo.
    echo 退出日志查看器
) else (
    echo.
    echo 无效选项，查看所有服务日志...
    echo.
    docker-compose -f docker-compose.remote.yml logs -f
)

echo.
echo ========================================
echo 日志查看完成
echo ========================================
pause
