# Magentic-UI 启动问题修复方案

## 问题描述
原始命令 `magentic-ui --config config.yaml --host 127.0.0.1 --port 8081` 报错：
```
magentic-ui:无法将"magnetic-ui"项识别为cmdlet函数、脚本文件
```

## 问题原因
1. **项目未安装**: `magentic_ui` 包没有安装到虚拟环境中
2. **依赖缺失**: 缺少 `playwright`、`tldextract` 等关键依赖
3. **命令不可用**: 由于包未安装，CLI 命令无法识别

## 解决方案

### 方案一：完整安装（推荐）
```bash
# 1. 激活虚拟环境
magentic-env\Scripts\activate.bat

# 2. 安装项目和依赖
pip install -e .
pip install playwright tldextract docker alembic psycopg

# 3. 安装 Playwright 浏览器
playwright install

# 4. 启动应用
magentic-ui --config config.yaml --host 127.0.0.1 --port 8081 --run-without-docker
```

### 方案二：简化版启动（当前可用）
```bash
# 1. 激活虚拟环境
magentic-env\Scripts\activate.bat

# 2. 使用简化服务器
python minimal_server.py
```

### 方案三：使用修复的启动脚本
```bash
# 1. 激活虚拟环境
magentic-env\Scripts\activate.bat

# 2. 使用修复脚本
python run_magentic_ui.py
```

## 当前状态

✅ **已修复的问题**:
- 修复了 `tldextract` 导入错误
- 修复了 `playwright` 导入错误
- 创建了简化版服务器
- 服务器已成功启动在 http://127.0.0.1:8081

✅ **可用功能**:
- Web 界面访问
- API 文档查看
- 配置文件读取
- 健康检查

⚠️ **限制**:
- 简化版不包含完整的 AI 代理功能
- 无浏览器自动化功能
- 无 Docker 支持

## 访问地址

- **主页**: http://127.0.0.1:8081
- **API 文档**: http://127.0.0.1:8081/docs
- **健康检查**: http://127.0.0.1:8081/health
- **配置信息**: http://127.0.0.1:8081/config

## 文件说明

- `minimal_server.py`: 简化版服务器（当前运行中）
- `run_magentic_ui.py`: 修复版启动脚本
- `start_simple.py`: 另一个启动选项
- `config.yaml`: 配置文件（包含 ModelScope API 配置）

## 下一步建议

1. **测试当前服务器**: 访问 http://127.0.0.1:8081 确认服务正常
2. **安装完整依赖**: 如需完整功能，按方案一安装所有依赖
3. **配置验证**: 检查 ModelScope API 密钥是否有效

## 技术修改

1. **修改了依赖导入**: 在 `_web_surfer.py` 和 `url_status_manager.py` 中添加了 try-except 处理
2. **创建了备用启动方案**: 提供多种启动选项
3. **环境变量设置**: 正确配置了运行环境

服务器现在已经成功运行，可以通过浏览器访问！
