#!/usr/bin/env python3
"""
测试环境变量配置
"""

import os
from pathlib import Path

def load_env_file(env_file_path):
    """加载环境变量文件"""
    if not Path(env_file_path).exists():
        print(f"❌ 环境变量文件 {env_file_path} 不存在!")
        return False
    
    with open(env_file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            # 跳过注释和空行
            if not line or line.startswith('#'):
                continue
            
            # 解析环境变量
            if '=' in line:
                key, value = line.split('=', 1)
                os.environ[key] = value
                print(f"✅ 已设置 {key}={value}")
    
    return True

def test_docker_host():
    """测试 DOCKER_HOST 配置"""
    docker_host = os.environ.get('DOCKER_HOST')
    if docker_host:
        print(f"✅ DOCKER_HOST: {docker_host}")
        return True
    else:
        print("❌ DOCKER_HOST 未设置")
        return False

def test_modelscope_config():
    """测试 ModelScope 配置"""
    api_key = os.environ.get('OPENAI_API_KEY')
    base_url = os.environ.get('OPENAI_BASE_URL')
    model = os.environ.get('DEFAULT_MODEL')
    
    if api_key:
        print(f"✅ OPENAI_API_KEY: {api_key[:20]}...")
    else:
        print("❌ OPENAI_API_KEY 未设置")
        
    if base_url:
        print(f"✅ OPENAI_BASE_URL: {base_url}")
    else:
        print("❌ OPENAI_BASE_URL 未设置")
        
    if model:
        print(f"✅ DEFAULT_MODEL: {model}")
    else:
        print("❌ DEFAULT_MODEL 未设置")
    
    return all([api_key, base_url, model])

def main():
    """主函数"""
    print("=" * 50)
    print("测试环境变量配置")
    print("=" * 50)
    
    # 加载环境变量文件
    if not load_env_file('.env.remote'):
        return 1
    
    print("\n" + "-" * 30)
    
    # 测试 Docker 配置
    docker_ok = test_docker_host()
    
    print("\n" + "-" * 30)
    
    # 测试 ModelScope 配置
    modelscope_ok = test_modelscope_config()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"环境变量加载: ✅ 成功")
    print(f"Docker 配置: {'✅ 正确' if docker_ok else '❌ 错误'}")
    print(f"ModelScope 配置: {'✅ 正确' if modelscope_ok else '❌ 错误'}")
    
    if docker_ok and modelscope_ok:
        print("\n🎉 环境变量配置正确!")
    else:
        print("\n⚠️  环境变量配置存在问题!")
    
    return 0

if __name__ == "__main__":
    exit(main())
