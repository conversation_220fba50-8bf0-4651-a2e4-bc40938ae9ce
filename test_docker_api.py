#!/usr/bin/env python3
"""
使用 Docker Python SDK 测试远程 Docker 连接
"""

import docker
import os

def test_docker_api_connection():
    """测试 Docker API 连接"""
    print("=" * 50)
    print("测试远程 Docker API 连接")
    print("=" * 50)
    
    try:
        # 创建 Docker 客户端，连接到远程 Docker
        client = docker.DockerClient(base_url='tcp://8.137.154.11:2375', timeout=30)
        
        # 获取版本信息
        version_info = client.version()
        print("✅ Docker API 连接成功!")
        print(f"Docker 版本: {version_info.get('Version', '未知')}")
        print(f"API 版本: {version_info.get('ApiVersion', '未知')}")
        print(f"操作系统: {version_info.get('Os', '未知')}")
        print(f"架构: {version_info.get('Arch', '未知')}")
        
        # 获取系统信息
        info = client.info()
        print(f"CPU: {info.get('NCPU', '未知')} 核")
        print(f"内存: {info.get('MemTotal', 0) / (1024**3):.2f} GB")
        
        return True
        
    except Exception as e:
        print(f"❌ Docker API 连接失败: {e}")
        return False

def test_docker_images(client):
    """测试 Docker 镜像"""
    print("\n" + "=" * 50)
    print("测试 Docker 镜像")
    print("=" * 50)
    
    try:
        # 列出镜像
        images = client.images.list()
        print(f"✅ 成功获取镜像列表，共 {len(images)} 个镜像")
        
        # 显示前几个镜像
        for i, image in enumerate(images[:5]):
            print(f"  镜像 {i+1}: {image.tags if image.tags else ['<none>']}")
            
        if len(images) > 5:
            print(f"  ... 还有 {len(images) - 5} 个镜像")
            
        return True
        
    except Exception as e:
        print(f"❌ 获取镜像列表失败: {e}")
        return False

def test_docker_containers(client):
    """测试 Docker 容器"""
    print("\n" + "=" * 50)
    print("测试 Docker 容器")
    print("=" * 50)
    
    try:
        # 列出正在运行的容器
        containers = client.containers.list()
        print(f"✅ 成功获取容器列表，共 {len(containers)} 个运行中的容器")
        
        # 显示容器信息
        for i, container in enumerate(containers):
            print(f"  容器 {i+1}: {container.name} ({container.status})")
            
        # 列出所有容器（包括停止的）
        all_containers = client.containers.list(all=True)
        print(f"总共容器数: {len(all_containers)}")
            
        return True
        
    except Exception as e:
        print(f"❌ 获取容器列表失败: {e}")
        return False

def main():
    """主函数"""
    print("Magentic-UI 远程 Docker API 测试")
    print("=" * 60)
    
    # 测试 Docker API 连接
    if test_docker_api_connection():
        # 创建客户端用于后续测试
        client = docker.DockerClient(base_url='tcp://8.137.154.11:2375', timeout=30)
        
        # 测试镜像
        test_docker_images(client)
        
        # 测试容器
        test_docker_containers(client)
        
        print("\n" + "=" * 60)
        print("🎉 Docker API 测试完成!")
        print("您可以使用 docker-compose.remote.yml 配置文件启动 Magentic-UI 服务")
    else:
        print("\n" + "=" * 60)
        print("❌ Docker API 测试失败!")
        print("请检查远程 Docker 服务器配置和网络连接")

if __name__ == "__main__":
    main()
