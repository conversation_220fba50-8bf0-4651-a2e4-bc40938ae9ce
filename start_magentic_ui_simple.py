#!/usr/bin/env python3
"""
简化的 Magentic-UI 启动脚本
绕过一些依赖问题，直接启动核心功能
"""

import sys
import os
import subprocess
from pathlib import Path

def main():
    """主函数"""
    # 添加 src 目录到 Python 路径
    src_path = Path(__file__).parent / "src"
    sys.path.insert(0, str(src_path))
    
    # 设置环境变量
    os.environ["PYTHONPATH"] = str(src_path)
    
    # 尝试直接运行 CLI
    try:
        from magentic_ui.backend.cli import run
        print("成功导入 magentic_ui.backend.cli")
        run()
    except ImportError as e:
        print(f"导入错误: {e}")
        print("尝试使用 uvicorn 直接启动...")
        
        # 尝试直接使用 uvicorn 启动
        try:
            import uvicorn
            # 设置基本环境变量
            os.environ["_HOST"] = "127.0.0.1"
            os.environ["_PORT"] = "8081"
            os.environ["_CONFIG"] = "config.yaml"
            os.environ["RUN_WITHOUT_DOCKER"] = "true"
            
            print("使用 uvicorn 启动应用...")
            uvicorn.run(
                "magentic_ui.backend.web.app:app",
                host="127.0.0.1",
                port=8081,
                reload=False
            )
        except ImportError:
            print("uvicorn 未安装，尝试使用 python -m 方式启动...")
            
            # 最后的备用方案
            cmd = [
                sys.executable, "-m", "uvicorn",
                "magentic_ui.backend.web.app:app",
                "--host", "127.0.0.1",
                "--port", "8081"
            ]
            
            env = os.environ.copy()
            env.update({
                "_HOST": "127.0.0.1",
                "_PORT": "8081",
                "_CONFIG": "config.yaml",
                "RUN_WITHOUT_DOCKER": "true",
                "PYTHONPATH": str(src_path)
            })
            
            subprocess.run(cmd, env=env)

if __name__ == "__main__":
    main()
