# Magentic-UI 远程 Docker 使用说明

## 概述

本说明文档介绍如何在没有本地 Docker 环境的 Windows 系统上使用远程 Docker 服务器运行 Magentic-UI。

## 前置条件

1. 已配置远程 Docker 服务器（IP: ************，端口: 2375）
2. 已配置 ModelScope API 密钥
3. 已安装 Docker Compose（本地或通过 WSL）

## 配置文件说明

### .env.remote

环境变量配置文件，包含：

- `DOCKER_HOST`: 远程 Docker 服务器地址
- `OPENAI_API_KEY`: ModelScope API 密钥
- `OPENAI_BASE_URL`: ModelScope API 基础 URL
- `DEFAULT_MODEL`: 默认使用的模型

### docker-compose.remote.yml

远程 Docker 部署配置文件，定义了以下服务：

- `magentic-ui`: 主应用服务（端口 8081）
- `novnc-browser`: 浏览器环境（端口 6080）
- `playwright`: 自动化测试服务

## 启动服务

### 方法一：使用管理脚本（推荐）

```
manage_remote_docker.bat
```

然后选择选项 1 启动服务。

### 方法二：直接使用 Docker Compose

```
docker-compose -f docker-compose.remote.yml up -d
```

## 访问应用

启动成功后，可以通过以下地址访问：

- Magentic-UI Web 界面：http://localhost:8081
- noVNC 浏览器环境：http://localhost:6080

## 管理服务

### 使用管理脚本

运行 `manage_remote_docker.bat` 可以进行以下操作：

1. 启动服务
2. 停止服务
3. 查看状态
4. 查看日志
5. 配置远程 Docker
6. 测试远程 Docker 连接
7. 测试环境变量配置

### 直接使用脚本

- 启动服务：`start_magentic_ui_remote.bat`
- 停止服务：`stop_magentic_ui_remote.bat`
- 查看状态：`check_status_remote.bat`
- 查看日志：`view_logs_remote.bat`

## 故障排除

### Docker 连接问题

1. 检查远程服务器 Docker 服务是否运行：
   ```
   systemctl status docker
   ```

2. 检查防火墙设置是否允许端口 2375 通信

3. 验证环境变量配置：
   ```
   python test_env_config.py
   ```

### ModelScope API 问题

1. 检查 API 密钥是否正确配置
2. 验证网络连接是否正常
3. 测试 API 连接：
   ```
   python test_remote_docker_connection.py
   ```

### 应用启动问题

1. 查看服务日志：
   ```
   view_logs_remote.bat
   ```
   或
   ```
   docker-compose -f docker-compose.remote.yml logs
   ```

2. 检查配置文件是否正确

3. 重新启动服务

## 常用命令

### 查看运行中的容器

```
docker-compose -f docker-compose.remote.yml ps
```

### 查看服务日志

```
docker-compose -f docker-compose.remote.yml logs -f
```

### 停止服务

```
docker-compose -f docker-compose.remote.yml down
```

### 停止服务并删除数据卷

```
docker-compose -f docker-compose.remote.yml down -v
```

## 数据持久化

默认情况下，应用数据会保存在远程服务器的 Docker 卷中。如果需要清除数据，可以在停止服务时使用 `-v` 参数删除数据卷。

## 安全注意事项

1. 远程 Docker API 端口（2375）未加密，建议在生产环境中使用 TLS 加密
2. 保护好 ModelScope API 密钥，避免泄露
3. 定期更新应用和服务镜像
