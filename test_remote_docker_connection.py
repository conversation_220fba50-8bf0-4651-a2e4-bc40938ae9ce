#!/usr/bin/env python3
"""
测试远程 Docker 连接（不依赖本地 Docker CLI）
"""

import os
import sys
import requests
from pathlib import Path

def test_remote_docker_api():
    """通过 Docker API 测试远程 Docker 连接"""
    print("=" * 50)
    print("测试远程 Docker API 连接")
    print("=" * 50)
    
    # 获取 Docker 主机地址
    docker_host = os.environ.get('DOCKER_HOST', 'tcp://************:2375')
    print(f"DOCKER_HOST: {docker_host}")
    
    # 解析 Docker API 地址
    if docker_host.startswith('tcp://'):
        # 将 tcp:// 转换为 http://
        api_url = 'http://' + docker_host[6:]  # 去掉 'tcp://' 前缀
    else:
        print("❌ 无效的 DOCKER_HOST 格式")
        return False
    
    # 测试 Docker API 版本
    try:
        print("正在测试 Docker API 版本...")
        response = requests.get(f"{api_url}/version", timeout=10)
        if response.status_code == 200:
            version_info = response.json()
            print("✅ Docker API 连接成功!")
            print(f"  API Version: {version_info.get('ApiVersion', 'N/A')}")
            print(f"  Docker Version: {version_info.get('Version', 'N/A')}")
            print(f"  Operating System: {version_info.get('Os', 'N/A')}")
            print(f"  Architecture: {version_info.get('Arch', 'N/A')}")
            return True
        else:
            print(f"❌ Docker API 连接失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Docker API 连接错误: {e}")
        print("\n请检查:")
        print("1. 远程服务器 Docker 服务是否正在运行")
        print("2. 防火墙是否允许端口 2375 通信")
        print("3. 服务器 Docker 配置是否正确")
        return False
    except requests.exceptions.Timeout:
        print("❌ Docker API 连接超时!")
        print("请检查网络连接和远程服务器状态")
        return False
    except Exception as e:
        print(f"❌ Docker API 测试出错: {e}")
        return False

def test_docker_info():
    """获取 Docker 信息"""
    print("\n" + "=" * 50)
    print("获取 Docker 信息")
    print("=" * 50)
    
    # 获取 Docker 主机地址
    docker_host = os.environ.get('DOCKER_HOST', 'tcp://************:2375')
    
    # 解析 Docker API 地址
    if docker_host.startswith('tcp://'):
        # 将 tcp:// 转换为 http://
        api_url = 'http://' + docker_host[6:]  # 去掉 'tcp://' 前缀
    else:
        print("❌ 无效的 DOCKER_HOST 格式")
        return False
    
    try:
        print("正在获取 Docker 信息...")
        response = requests.get(f"{api_url}/info", timeout=15)
        if response.status_code == 200:
            info = response.json()
            print("✅ Docker 信息获取成功!")
            print(f"  Server Version: {info.get('ServerVersion', 'N/A')}")
            print(f"  Operating System: {info.get('OperatingSystem', 'N/A')}")
            print(f"  Architecture: {info.get('Architecture', 'N/A')}")
            print(f"  CPUs: {info.get('NCPU', 'N/A')}")
            print(f"  Memory: {info.get('MemTotal', 'N/A')}")
            print(f"  Containers: {info.get('Containers', 'N/A')}")
            print(f"  Images: {info.get('Images', 'N/A')}")
            return True
        else:
            print(f"❌ Docker 信息获取失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 获取 Docker 信息出错: {e}")
        return False

def test_modelscope_api():
    """测试 ModelScope API"""
    print("\n" + "=" * 50)
    print("测试 ModelScope API 连接")
    print("=" * 50)
    
    try:
        # 读取配置文件
        config_path = Path("config.yaml")
        if not config_path.exists():
            print("❌ 配置文件 config.yaml 不存在!")
            return False
            
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 获取 API 配置
        model_config = config['model_config_light']
        api_key = model_config['config']['api_key']
        base_url = model_config['config']['base_url']
        model = model_config['config']['model']
        
        print(f"Base URL: {base_url}")
        print(f"Model: {model}")
        
        # 测试 API 调用
        response = requests.post(
            f"{base_url}/chat/completions",
            headers={
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            },
            json={
                'model': model,
                'messages': [
                    {'role': 'user', 'content': 'Hello, please respond with "API test successful"'}
                ],
                'max_tokens': 20
            },
            timeout=30
        )
        
        print(f"HTTP Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API 测试成功!")
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"响应内容: {content}")
            return True
        else:
            print(f"❌ API 测试失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API 测试出错: {e}")
        return False

def main():
    """主函数"""
    print("Magentic-UI 远程 Docker 连接测试 (不依赖本地 Docker CLI)")
    print("=" * 60)
    
    # 加载环境变量
    env_file = Path(".env.remote")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                if '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
    
    # 测试 Docker API 连接
    docker_api_ok = test_remote_docker_api()
    
    if docker_api_ok:
        # 获取 Docker 信息
        test_docker_info()
    
    # 测试 ModelScope API
    api_ok = test_modelscope_api()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"Docker API 连接: {'✅ 成功' if docker_api_ok else '❌ 失败'}")
    print(f"ModelScope API: {'✅ 成功' if api_ok else '❌ 失败'}")
    
    if docker_api_ok and api_ok:
        print("\n🎉 所有测试通过! 您可以开始使用 Magentic-UI 了。")
        print("\n下一步建议:")
        print("1. 使用 docker-compose.remote.yml 启动服务:")
        print("   docker-compose -f docker-compose.remote.yml up -d")
        print("2. 访问 http://localhost:8081 使用 Magentic-UI")
    else:
        print("\n⚠️  部分测试失败，请检查配置。")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
