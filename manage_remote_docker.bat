@echo off
setlocal enabledelayedexpansion

:main_menu
cls
echo ========================================
echo Magentic-UI 远程 Docker 管理工具
echo ========================================
echo 当前时间: %date% %time%
echo 当前目录: %cd%
echo.

REM 检查环境变量文件
if exist ".env.remote" (
    REM 加载环境变量
    for /f "usebackq tokens=*" %%a in (`.env.remote`) do (
        set "line=%%a"
        if not "!line:~0,1!"=="#" if "!line!" neq "" (
            set "!line!"
        )
    )
    echo Docker 主机: %DOCKER_HOST%
    echo.
) else (
    echo ⚠️  环境变量文件 .env.remote 不存在
    echo.
)

echo 请选择操作:
echo.
echo [1] 启动服务 (start_magentic_ui_remote.bat)
echo [2] 停止服务 (stop_magentic_ui_remote.bat)
echo [3] 查看状态 (check_status_remote.bat)
echo [4] 查看日志 (view_logs_remote.bat)
echo [5] 配置远程 Docker (setup_remote_docker.bat)
echo [6] 测试远程 Docker 连接 (test_remote_docker.py)
echo [7] 测试环境变量配置 (test_env_config.py)
echo.
echo [0] 退出
echo.
echo ========================================

set /p choice=请输入选项 (0-7): 

if "%choice%"=="1" (
    echo.
    echo 正在启动服务...
    call start_magentic_ui_remote.bat
    echo.
    echo 按任意键返回主菜单...
    pause >nul
    goto main_menu
) else if "%choice%"=="2" (
    echo.
    echo 正在停止服务...
    call stop_magentic_ui_remote.bat
    echo.
    echo 按任意键返回主菜单...
    pause >nul
    goto main_menu
) else if "%choice%"=="3" (
    echo.
    echo 正在检查状态...
    call check_status_remote.bat
    echo.
    echo 按任意键返回主菜单...
    pause >nul
    goto main_menu
) else if "%choice%"=="4" (
    echo.
    echo 正在查看日志...
    call view_logs_remote.bat
    echo.
    echo 按任意键返回主菜单...
    pause >nul
    goto main_menu
) else if "%choice%"=="5" (
    echo.
    echo 正在配置远程 Docker...
    call setup_remote_docker.bat
    echo.
    echo 按任意键返回主菜单...
    pause >nul
    goto main_menu
) else if "%choice%"=="6" (
    echo.
    echo 正在测试远程 Docker 连接...
    python test_remote_docker.py
    echo.
    echo 按任意键返回主菜单...
    pause >nul
    goto main_menu
) else if "%choice%"=="7" (
    echo.
    echo 正在测试环境变量配置...
    python test_env_config.py
    echo.
    echo 按任意键返回主菜单...
    pause >nul
    goto main_menu
) else if "%choice%"=="0" (
    echo.
    echo 感谢使用 Magentic-UI 远程 Docker 管理工具！
    echo.
    pause
    exit /b 0
) else (
    echo.
    echo 无效选项，请重新选择。
    echo.
    echo 按任意键返回主菜单...
    pause >nul
    goto main_menu
)
