@echo off
setlocal enabledelayedexpansion

echo ========================================
echo 启动 Magentic-UI (远程 Docker 模式)
echo ========================================

REM 检查命令行参数
set "BUILD_IMAGES=false"
set "DETACH_MODE=true"

:parse_args
if "%1"=="" goto args_done
if "%1"=="--build" (
    set "BUILD_IMAGES=true"
    shift
    goto parse_args
)
if "%1"=="--no-detach" (
    set "DETACH_MODE=false"
    shift
    goto parse_args
)
shift
goto parse_args

:args_done

REM 设置环境变量文件
echo 加载环境变量文件: .env.remote

REM 检查环境变量文件是否存在
if not exist ".env.remote" (
    echo ❌ 环境变量文件 .env.remote 不存在!
    echo 请确保 .env.remote 文件在当前目录中
    echo 或运行 setup_remote_docker.bat 创建该文件
    pause
    exit /b 1
)

REM 加载环境变量
for /f "usebackq tokens=*" %%a in (`.env.remote`) do (
    set "line=%%a"
    if not "!line:~0,1!"=="#" if "!line!" neq "" (
        set "!line!"
    )
)

echo 已设置 DOCKER_HOST=%DOCKER_HOST%

REM 检查 Docker 连接
echo.
echo 正在检查 Docker 连接...
docker version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Docker 连接正常
) else (
    echo ❌ Docker 连接失败!
    echo 请确保:
    echo 1. 远程服务器 Docker 已正确配置并运行
    echo 2. 防火墙已允许访问端口 2375
    echo 3. 本地已运行 setup_remote_docker.bat 配置连接
    pause
    exit /b 1
)

REM 检查配置文件
if not exist "config.yaml" (
    echo ⚠️  配置文件 config.yaml 不存在，将使用默认配置
    echo 请确保项目根目录中有正确的 config.yaml 文件
)

REM 创建数据目录
if not exist "data" (
    echo 创建数据目录: data
    mkdir data
)

REM 构建镜像（如果需要）
if "%BUILD_IMAGES%"=="true" (
    echo.
    echo 正在构建 Docker 镜像...
    cd docker
    if exist "build-all.sh" (
        echo 执行 build-all.sh...
        call build-all.sh
    ) else (
        echo ⚠️  build-all.sh 不存在，跳过构建
    )
    cd ..
)

REM 启动 Magentic-UI 服务
echo.
echo 正在启动 Magentic-UI 服务...
echo 使用配置文件: docker-compose.remote.yml

if "%DETACH_MODE%"=="true" (
    echo 以分离模式启动服务...
    docker-compose -f docker-compose.remote.yml up -d
) else (
    echo 以前台模式启动服务...
    docker-compose -f docker-compose.remote.yml up
)

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Magentic-UI 服务启动成功!
    echo.
    echo 访问地址:
    echo   Magentic-UI: http://localhost:8081
    echo   noVNC Browser: http://localhost:6080
    echo.
    echo 常用管理命令:
    echo   查看日志: docker-compose -f docker-compose.remote.yml logs -f
    echo   停止服务: docker-compose -f docker-compose.remote.yml down
    echo   重启服务: docker-compose -f docker-compose.remote.yml restart
    echo   查看状态: docker-compose -f docker-compose.remote.yml ps
) else (
    echo.
    echo ❌ Magentic-UI 服务启动失败!
    echo 请检查错误信息并重试
    echo.
    echo 尝试查看详细日志:
    echo   docker-compose -f docker-compose.remote.yml logs
)

echo.
echo ========================================
echo 启动脚本执行完成
echo ========================================
pause
