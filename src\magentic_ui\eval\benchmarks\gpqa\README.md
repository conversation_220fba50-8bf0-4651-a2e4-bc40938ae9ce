# GPQA Eval

Run instructions: 

```python
python experiments/eval/run.py --current-dir . --dataset GPQA --split main --run-id 0 --simulated-user-type none --parallel 1 --config experiments/endpoint_configs/config_template.yaml --mode run --system-type LLM
```

```
@inproceedings{rein2024gpqa,
      title={{GPQA}: A Graduate-Level Google-Proof Q\&A Benchmark},
      author={<PERSON> and <PERSON> and <PERSON><PERSON> and <PERSON> and <PERSON> and <PERSON> and <PERSON> and <PERSON>},
      booktitle={First Conference on Language Modeling},
      year={2024},
      url={https://openreview.net/forum?id=Ti67584b98}
}
```