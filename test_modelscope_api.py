#!/usr/bin/env python3
"""
ModelScope API 测试脚本
用于验证 ModelScope API 配置是否正确工作
"""

import asyncio
import os
import yaml
from pathlib import Path
from dotenv import load_dotenv
from openai import OpenAI

# 加载环境变量
load_dotenv()

def test_direct_openai_client():
    """直接使用 OpenAI 客户端测试 ModelScope API"""
    print("=" * 60)
    print("测试 1: 直接使用 OpenAI 客户端")
    print("=" * 60)
    
    try:
        client = OpenAI(
            base_url='https://api-inference.modelscope.cn/v1',
            api_key='ms-4ab3c32e-dd51-426c-80f1-7e058b32935c',
        )
        
        print("正在调用 ModelScope API...")
        response = client.chat.completions.create(
            model='Qwen/Qwen3-Coder-480B-A35B-Instruct',
            messages=[
                {
                    'role': 'system',
                    'content': 'You are a helpful assistant.'
                },
                {
                    'role': 'user',
                    'content': '你好，请简单介绍一下你自己'
                }
            ],
            stream=False,
            max_tokens=200
        )
        
        print("✅ API 调用成功!")
        print(f"模型: {response.model}")
        print(f"响应: {response.choices[0].message.content}")
        print(f"Token 使用: {response.usage}")
        return True
        
    except Exception as e:
        print(f"❌ API 调用失败: {e}")
        return False

def test_streaming_response():
    """测试流式响应"""
    print("\n" + "=" * 60)
    print("测试 2: 流式响应测试")
    print("=" * 60)
    
    try:
        client = OpenAI(
            base_url='https://api-inference.modelscope.cn/v1',
            api_key='ms-4ab3c32e-dd51-426c-80f1-7e058b32935c',
        )
        
        print("正在测试流式响应...")
        response = client.chat.completions.create(
            model='Qwen/Qwen3-Coder-480B-A35B-Instruct',
            messages=[
                {
                    'role': 'system',
                    'content': 'You are a helpful coding assistant.'
                },
                {
                    'role': 'user',
                    'content': '请写一个简单的 Python 函数来计算斐波那契数列'
                }
            ],
            stream=True,
            max_tokens=300
        )
        
        print("✅ 流式响应开始:")
        print("-" * 40)
        full_response = ""
        for chunk in response:
            if chunk.choices[0].delta.content:
                content = chunk.choices[0].delta.content
                print(content, end='', flush=True)
                full_response += content
        
        print("\n" + "-" * 40)
        print("✅ 流式响应测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 流式响应测试失败: {e}")
        return False

async def test_autogen_integration():
    """测试与 AutoGen 的集成"""
    print("\n" + "=" * 60)
    print("测试 3: AutoGen 集成测试")
    print("=" * 60)
    
    try:
        # 检查是否可以导入 autogen 相关模块
        from autogen_core.models import ChatCompletionClient, UserMessage
        
        # 加载配置文件
        config_path = Path("config.yaml")
        if not config_path.exists():
            print("❌ config.yaml 文件不存在")
            return False
            
        with open(config_path, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)
        
        # 获取模型客户端配置
        client_config = config.get("orchestrator_client")
        if not client_config:
            print("❌ 配置文件中未找到 orchestrator_client")
            return False
            
        print("正在初始化 AutoGen ChatCompletionClient...")
        client = ChatCompletionClient.load_component(client_config)
        
        print("正在测试 AutoGen 客户端...")
        response = await client.create(
            messages=[UserMessage(content="你好，请用中文回答：什么是人工智能？", source="user")]
        )
        
        print("✅ AutoGen 集成测试成功!")
        print(f"响应: {response.content}")
        
        await client.close()
        return True
        
    except ImportError as e:
        print(f"❌ AutoGen 模块导入失败: {e}")
        print("请确保已安装 autogen 相关依赖")
        return False
    except Exception as e:
        print(f"❌ AutoGen 集成测试失败: {e}")
        return False

def test_environment_variables():
    """测试环境变量配置"""
    print("\n" + "=" * 60)
    print("测试 4: 环境变量配置检查")
    print("=" * 60)
    
    required_vars = [
        "OPENAI_API_KEY",
        "OPENAI_BASE_URL",
        "DEFAULT_MODEL"
    ]
    
    all_good = True
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: 未设置")
            all_good = False
    
    return all_good

async def main():
    """主测试函数"""
    print("ModelScope API 配置测试")
    print("=" * 60)
    
    results = []
    
    # 测试环境变量
    results.append(test_environment_variables())
    
    # 测试直接 API 调用
    results.append(test_direct_openai_client())
    
    # 测试流式响应
    results.append(test_streaming_response())
    
    # 测试 AutoGen 集成
    results.append(await test_autogen_integration())
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过! ModelScope API 配置正确")
    else:
        print("⚠️  部分测试失败，请检查配置")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
