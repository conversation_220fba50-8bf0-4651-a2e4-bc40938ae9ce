#!/usr/bin/env python3
"""
简化的 Magentic-UI 启动脚本
直接启动 FastAPI 应用，绕过复杂的依赖
"""

import sys
import os
import uvicorn
from pathlib import Path

def main():
    """主函数"""
    # 添加 src 目录到 Python 路径
    src_path = Path(__file__).parent / "src"
    sys.path.insert(0, str(src_path))
    
    # 设置环境变量
    workspace_root = str(Path.home() / ".magentic_ui")
    os.environ.update({
        "PYTHONPATH": str(src_path),
        "_HOST": "127.0.0.1",
        "_PORT": "8081",
        "_CONFIG": "config.yaml",
        "RUN_WITHOUT_DOCKER": "true",
        "_API_DOCS": "true",
        "_APPDIR": str(Path.home() / ".magentic_ui"),
        "INSIDE_DOCKER": "0",
        "INTERNAL_WORKSPACE_ROOT": workspace_root,
        "EXTERNAL_WORKSPACE_ROOT": workspace_root
    })
    
    print("启动 Magentic-UI (简化模式)...")
    print(f"主机: 127.0.0.1")
    print(f"端口: 8081")
    print(f"配置文件: config.yaml")
    print(f"运行模式: 无 Docker")
    print()
    
    try:
        # 直接使用 uvicorn 启动
        uvicorn.run(
            "magentic_ui.backend.web.app:app",
            host="127.0.0.1",
            port=8081,
            reload=False,
            log_level="info"
        )
    except Exception as e:
        print(f"启动失败: {e}")
        print("请确保已安装所有必要的依赖")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
