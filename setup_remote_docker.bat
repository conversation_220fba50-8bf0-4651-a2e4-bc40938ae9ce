@echo off
echo 设置远程 Docker 连接...
echo ================================

REM 设置远程 Docker 主机
set DOCKER_HOST=tcp://************:2375
echo 已设置 DOCKER_HOST=%DOCKER_HOST%

REM 验证 Docker 连接
echo.
echo 正在验证 Docker 连接...
docker version
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Docker 连接成功!
    echo.
    echo 当前 Docker 信息:
    docker info
) else (
    echo.
    echo ❌ Docker 连接失败!
    echo 请检查:
    echo 1. 远程服务器 Docker 服务是否正在运行
    echo 2. 防火墙是否允许端口 2375 通信
    echo 3. 服务器 Docker 配置是否正确
)

echo.
echo ================================
echo 远程 Docker 配置完成
echo 要在其他终端中使用此配置，请运行:
echo set DOCKER_HOST=tcp://************:2375
pause
