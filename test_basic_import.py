#!/usr/bin/env python3
"""
基础导入测试脚本
测试 Magentic-UI 的基本模块是否可以导入
"""

import sys
import traceback

def test_import(module_name, description=""):
    """测试模块导入"""
    try:
        __import__(module_name)
        print(f"✅ {module_name} - {description}")
        return True
    except Exception as e:
        print(f"❌ {module_name} - {description}")
        print(f"   错误: {e}")
        return False

def main():
    print("Magentic-UI 基础导入测试")
    print("=" * 50)
    
    # 测试基础 Python 模块
    basic_modules = [
        ("os", "操作系统接口"),
        ("sys", "系统相关参数"),
        ("pathlib", "路径操作"),
        ("asyncio", "异步编程"),
        ("yaml", "YAML 解析"),
        ("dotenv", "环境变量"),
        ("openai", "OpenAI 客户端"),
    ]
    
    print("\n1. 基础模块测试:")
    basic_success = 0
    for module, desc in basic_modules:
        if test_import(module, desc):
            basic_success += 1
    
    print(f"\n基础模块: {basic_success}/{len(basic_modules)} 通过")
    
    # 测试 AutoGen 模块
    autogen_modules = [
        ("autogen_core", "AutoGen 核心"),
        ("autogen_core.models", "AutoGen 模型"),
        ("autogen_agentchat", "AutoGen 聊天"),
    ]
    
    print("\n2. AutoGen 模块测试:")
    autogen_success = 0
    for module, desc in autogen_modules:
        if test_import(module, desc):
            autogen_success += 1
    
    print(f"\nAutoGen 模块: {autogen_success}/{len(autogen_modules)} 通过")
    
    # 测试 Magentic-UI 模块
    print("\n3. Magentic-UI 模块测试:")
    
    # 先测试最基础的
    try:
        import magentic_ui
        print("✅ magentic_ui - 主模块")
        magentic_success = 1
        
        # 测试子模块
        submodules = [
            ("magentic_ui.backend", "后端模块"),
            ("magentic_ui.backend.web", "Web 模块"),
            ("magentic_ui.backend.web.config", "配置模块"),
        ]
        
        for module, desc in submodules:
            if test_import(module, desc):
                magentic_success += 1
        
        print(f"\nMagentic-UI 模块: {magentic_success}/{len(submodules)+1} 通过")
        
    except Exception as e:
        print(f"❌ magentic_ui - 主模块导入失败")
        print(f"   错误: {e}")
        print(f"   详细错误信息:")
        traceback.print_exc()
        magentic_success = 0
    
    # 总结
    total_modules = len(basic_modules) + len(autogen_modules) + 4  # 4 个 magentic-ui 模块
    total_success = basic_success + autogen_success + magentic_success
    
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"总模块数: {total_modules}")
    print(f"成功导入: {total_success}")
    print(f"成功率: {total_success/total_modules*100:.1f}%")
    
    if total_success == total_modules:
        print("🎉 所有模块导入成功！")
        return True
    else:
        print("⚠️  部分模块导入失败，请检查依赖安装")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
