#!/usr/bin/env python3
"""
使用 Docker API 停止远程 Magentic-UI 服务
"""

import requests
import time

# 远程 Docker API 地址
DOCKER_API_URL = "http://8.137.154.11:2375"

def check_docker_api():
    """检查 Docker API 是否可用"""
    try:
        response = requests.get(f"{DOCKER_API_URL}/version", timeout=10)
        if response.status_code == 200:
            version_info = response.json()
            print("✅ Docker API 连接成功!")
            print(f"Docker 版本: {version_info.get('Version', '未知')}")
            return True
        else:
            print(f"❌ Docker API 连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Docker API 连接出错: {e}")
        return False

def stop_container(container_name):
    """停止容器"""
    try:
        response = requests.post(
            f"{DOCKER_API_URL}/containers/{container_name}/stop",
            timeout=30
        )
        
        if response.status_code == 204:
            print(f"✅ 容器 {container_name} 停止成功!")
            return True
        elif response.status_code == 304:
            print(f"ℹ️  容器 {container_name} 已停止")
            return True
        elif response.status_code == 404:
            print(f"⚠️  容器 {container_name} 不存在")
            return True
        else:
            print(f"❌ 停止容器 {container_name} 失败: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"❌ 停止容器 {container_name} 出错: {e}")
        return False

def remove_container(container_name):
    """删除容器"""
    try:
        response = requests.delete(
            f"{DOCKER_API_URL}/containers/{container_name}?force=true",
            timeout=30
        )
        
        if response.status_code == 204:
            print(f"✅ 容器 {container_name} 删除成功!")
            return True
        elif response.status_code == 404:
            print(f"⚠️  容器 {container_name} 不存在")
            return True
        else:
            print(f"❌ 删除容器 {container_name} 失败: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"❌ 删除容器 {container_name} 出错: {e}")
        return False

def main():
    """主函数"""
    print("Magentic-UI 远程服务停止脚本")
    print("=" * 60)
    
    # 检查 Docker API 连接
    if not check_docker_api():
        print("❌ 无法连接到远程 Docker API，请检查配置")
        return 1
    
    # 停止并删除 Magentic-UI 容器
    print("\n" + "=" * 50)
    print("停止 Magentic-UI 服务")
    print("=" * 50)
    
    if not stop_container("magentic-ui"):
        print("❌ 停止 Magentic-UI 容器失败")
    
    # 停止并删除浏览器容器
    print("\n" + "=" * 50)
    print("停止浏览器服务")
    print("=" * 50)
    
    if not stop_container("magentic-ui-browser"):
        print("❌ 停止浏览器容器失败")
    
    # 等待容器停止
    print("\n等待容器停止...")
    time.sleep(5)
    
    # 删除容器
    print("\n" + "=" * 50)
    print("删除容器")
    print("=" * 50)
    
    remove_container("magentic-ui")
    remove_container("magentic-ui-browser")
    
    print("\n" + "=" * 60)
    print("🎉 服务停止完成!")
    print("所有容器已停止并删除")
    
    return 0

if __name__ == "__main__":
    exit(main())
