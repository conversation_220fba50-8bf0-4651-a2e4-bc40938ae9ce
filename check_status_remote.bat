@echo off
setlocal enabledelayedexpansion

echo ========================================
echo 检查 Magentic-UI 状态 (远程 Docker 模式)
echo ========================================

REM 设置环境变量文件
echo 加载环境变量文件: .env.remote

REM 检查环境变量文件是否存在
if not exist ".env.remote" (
    echo ⚠️  环境变量文件 .env.remote 不存在!
    echo 尝试使用默认的 Docker 主机设置...
    set "DOCKER_HOST=tcp://************:2375"
) else (
    REM 加载环境变量
    for /f "usebackq tokens=*" %%a in (`.env.remote`) do (
        set "line=%%a"
        if not "!line:~0,1!"=="#" if "!line!" neq "" (
            set "!line!"
        )
    )
)

echo 当前 DOCKER_HOST=%DOCKER_HOST%

REM 检查 Docker 连接
echo.
echo [1/5] 检查 Docker 连接...
docker version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Docker 连接正常
    set "DOCKER_STATUS=✅ 正常"
) else (
    echo ❌ Docker 连接失败
    set "DOCKER_STATUS=❌ 失败"
)

REM 检查 docker-compose 文件是否存在
echo.
echo [2/5] 检查配置文件...
if not exist "docker-compose.remote.yml" (
    echo ❌ docker-compose.remote.yml 文件不存在!
    set "CONFIG_STATUS=❌ 配置文件缺失"
    pause
    exit /b 1
) else (
    echo ✅ 配置文件存在
    set "CONFIG_STATUS=✅ 正常"
)

REM 验证 docker-compose 配置
echo.
echo [3/5] 验证 Docker Compose 配置...
docker-compose -f docker-compose.remote.yml config >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Docker Compose 配置正确
    set "COMPOSE_CONFIG_STATUS=✅ 正确"
) else (
    echo ❌ Docker Compose 配置错误
    set "COMPOSE_CONFIG_STATUS=❌ 错误"
)

REM 检查服务状态
echo.
echo [4/5] 检查服务状态...
docker-compose -f docker-compose.remote.yml ps >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Docker Compose 服务状态可查询
    set "SERVICE_STATUS=✅ 可查询"
    
    REM 获取详细服务状态
    echo.
    echo 服务状态详情:
    docker-compose -f docker-compose.remote.yml ps
    
    REM 检查各个服务
    echo.
    echo 检查各个服务...
    
    REM 检查 magentic-ui-app 服务
    docker-compose -f docker-compose.remote.yml ps | findstr "magentic-ui-app" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        docker-compose -f docker-compose.remote.yml ps | findstr "magentic-ui-app" | findstr "Up" >nul 2>&1
        if %ERRORLEVEL% EQU 0 (
            echo   magentic-ui-app: ✅ 运行中
            set "APP_STATUS=✅ 运行中"
        ) else (
            echo   magentic-ui-app: ⚠️  未运行
            set "APP_STATUS=⚠️  未运行"
        )
    ) else (
        echo   magentic-ui-app: ❌ 未找到
        set "APP_STATUS=❌ 未找到"
    )
    
    REM 检查 magentic-ui-browser 服务
    docker-compose -f docker-compose.remote.yml ps | findstr "magentic-ui-browser" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        docker-compose -f docker-compose.remote.yml ps | findstr "magentic-ui-browser" | findstr "Up" >nul 2>&1
        if %ERRORLEVEL% EQU 0 (
            echo   magentic-ui-browser: ✅ 运行中
            set "BROWSER_STATUS=✅ 运行中"
        ) else (
            echo   magentic-ui-browser: ⚠️  未运行
            set "BROWSER_STATUS=⚠️  未运行"
        )
    ) else (
        echo   magentic-ui-browser: ❌ 未找到
        set "BROWSER_STATUS=❌ 未找到"
    )
) else (
    echo ⚠️  无法查询 Docker Compose 服务状态
    set "SERVICE_STATUS=⚠️  无法查询"
    set "APP_STATUS=⚠️  未知"
    set "BROWSER_STATUS=⚠️  未知"
)

REM 检查端口连接
echo.
echo [5/5] 检查端口连接...
set "PORT_8081_STATUS=⚠️  未检查"
set "PORT_6080_STATUS=⚠  未检查"

REM 检查本地端口 8081 是否可访问
docker-compose -f docker-compose.remote.yml port magentic-ui-app 8081 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo   端口 8081 映射: ✅ 正常
    set "PORT_8081_STATUS=✅ 正常"
) else (
    echo   端口 8081 映射: ⚠️  无法确定
    set "PORT_8081_STATUS=⚠️  无法确定"
)

REM 检查本地端口 6080 是否可访问
docker-compose -f docker-compose.remote.yml port magentic-ui-browser 6080 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo   端口 6080 映射: ✅ 正常
    set "PORT_6080_STATUS=✅ 正常"
) else (
    echo   端口 6080 映射: ⚠️  无法确定
    set "PORT_6080_STATUS=⚠️  无法确定"
)

REM 显示状态报告
echo.
echo ========================================
echo 状态检查报告
echo ========================================
echo Docker 连接:           %DOCKER_STATUS%
echo 配置文件:             %CONFIG_STATUS%
echo Compose 配置:         %COMPOSE_CONFIG_STATUS%
echo 服务状态:             %SERVICE_STATUS%
echo   magentic-ui-app:    %APP_STATUS%
echo   magentic-ui-browser: %BROWSER_STATUS%
echo 端口映射:
echo   8081 (Magentic-UI):  %PORT_8081_STATUS%
echo   6080 (noVNC):       %PORT_6080_STATUS%
echo ========================================

REM 总体状态评估
set "OVERALL_STATUS=✅ 正常"
if "%DOCKER_STATUS%"=="❌ 失败" set "OVERALL_STATUS=❌ 错误"
if "%CONFIG_STATUS%"=="❌ 配置文件缺失" set "OVERALL_STATUS=❌ 错误"
if "%COMPOSE_CONFIG_STATUS%"=="❌ 错误" set "OVERALL_STATUS=❌ 错误"
if "%APP_STATUS%"=="❌ 未找到" set "OVERALL_STATUS=❌ 错误"
if "%BROWSER_STATUS%"=="❌ 未找到" set "OVERALL_STATUS=❌ 错误"

echo 总体状态:             %OVERALL_STATUS%
echo ========================================

REM 提供修复建议
if not "%OVERALL_STATUS%"=="✅ 正常" (
    echo.
    echo 修复建议:
    if "%DOCKER_STATUS%"=="❌ 失败" (
        echo 1. 检查远程服务器 Docker 配置是否正确
        echo 2. 确认防火墙允许访问端口 2375
        echo 3. 验证本地网络连接到远程服务器
        echo 4. 重新运行 setup_remote_docker.bat 配置连接
    )
    if "%CONFIG_STATUS%"=="❌ 配置文件缺失" (
        echo 1. 确保在正确的项目目录中运行此脚本
        echo 2. 检查 docker-compose.remote.yml 文件是否存在
    )
    if "%COMPOSE_CONFIG_STATUS%"=="❌ 错误" (
        echo 1. 检查 docker-compose.remote.yml 文件语法是否正确
        echo 2. 验证环境变量是否正确设置
    )
    if "%APP_STATUS%"=="❌ 未找到" (
        echo 1. 尝试重新启动服务: start_magentic_ui_remote.bat
        echo 2. 检查服务配置是否正确
    )
    if "%BROWSER_STATUS%"=="❌ 未找到" (
        echo 1. 尝试重新启动服务: start_magentic_ui_remote.bat
        echo 2. 检查浏览器服务配置是否正确
    )
    echo.
)

echo 检查完成时间: %date% %time%
echo ========================================
pause
