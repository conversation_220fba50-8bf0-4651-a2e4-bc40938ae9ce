#!/usr/bin/env python3
"""
最小化的 Magentic-UI 服务器
绕过复杂依赖，提供基本的 Web 界面
"""

import sys
import os
import json
from pathlib import Path
from typing import Dict, Any

# 添加 src 目录到 Python 路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

try:
    from fastapi import FastAPI, HTTPException
    from fastapi.responses import JSONResponse, HTMLResponse
    from fastapi.staticfiles import StaticFiles
    import uvicorn
    import yaml
except ImportError as e:
    print(f"缺少必要依赖: {e}")
    print("请运行: pip install fastapi uvicorn pyyaml")
    sys.exit(1)

# 设置环境变量
os.environ.update({
    "PYTHONPATH": str(src_path),
    "_HOST": "127.0.0.1",
    "_PORT": "8081",
    "_CONFIG": "config.yaml",
    "RUN_WITHOUT_DOCKER": "true",
    "_API_DOCS": "true",
    "_APPDIR": str(Path.home() / ".magentic_ui"),
    "INSIDE_DOCKER": "0",
})

app = FastAPI(
    title="Magentic-UI (简化版)",
    description="Web 浏览助手 - 简化版本",
    version="0.1.0"
)

@app.get("/")
async def root():
    """根路径"""
    return HTMLResponse("""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Magentic-UI</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .status { padding: 20px; background: #e8f5e8; border-radius: 5px; margin: 20px 0; }
            .error { background: #ffe8e8; }
            .info { background: #e8f0ff; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🧙‍♂️ Magentic-UI</h1>
            <div class="status">
                <h3>✅ 服务器运行中</h3>
                <p>主机: 127.0.0.1:8081</p>
                <p>模式: 简化版 (无 Docker)</p>
                <p>配置: config.yaml</p>
            </div>
            
            <div class="info">
                <h3>ℹ️ 当前状态</h3>
                <p>这是 Magentic-UI 的简化版本。完整功能需要安装所有依赖。</p>
                <p>要安装完整版本，请运行:</p>
                <pre>pip install playwright tldextract docker alembic psycopg</pre>
            </div>
            
            <h3>📋 API 端点</h3>
            <ul>
                <li><a href="/docs">API 文档</a></li>
                <li><a href="/health">健康检查</a></li>
                <li><a href="/config">配置信息</a></li>
            </ul>
        </div>
    </body>
    </html>
    """)

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "ok", "message": "Magentic-UI 简化版运行正常"}

@app.get("/config")
async def get_config():
    """获取配置信息"""
    try:
        config_path = Path("config.yaml")
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return {"status": "loaded", "config": config}
        else:
            return {"status": "not_found", "message": "配置文件 config.yaml 不存在"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.get("/status")
async def get_status():
    """获取系统状态"""
    return {
        "server": "running",
        "mode": "simplified",
        "docker": False,
        "host": "127.0.0.1",
        "port": 8081,
        "dependencies": {
            "playwright": False,
            "tldextract": False,
            "docker": False
        }
    }

def main():
    """主函数"""
    print("=" * 60)
    print("🧙‍♂️ Magentic-UI 简化版服务器")
    print("=" * 60)
    print(f"🌐 访问地址: http://127.0.0.1:8081")
    print(f"📚 API 文档: http://127.0.0.1:8081/docs")
    print(f"⚙️  配置文件: config.yaml")
    print(f"🐳 Docker: 禁用")
    print("=" * 60)
    print()
    
    try:
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8081,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
