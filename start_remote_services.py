#!/usr/bin/env python3
"""
使用 Docker API 启动远程 Magentic-UI 服务
"""

import requests
import json
import time
import os
from pathlib import Path

# 远程 Docker API 地址
DOCKER_API_URL = "http://8.137.154.11:2375"

def check_docker_api():
    """检查 Docker API 是否可用"""
    try:
        response = requests.get(f"{DOCKER_API_URL}/version", timeout=10)
        if response.status_code == 200:
            version_info = response.json()
            print("✅ Docker API 连接成功!")
            print(f"Docker 版本: {version_info.get('Version', '未知')}")
            print(f"API 版本: {version_info.get('ApiVersion', '未知')}")
            return True
        else:
            print(f"❌ Docker API 连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Docker API 连接出错: {e}")
        return False

def read_env_file():
    """读取 .env.remote 文件获取环境变量"""
    env_vars = {}
    env_file = Path(".env.remote")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
        print(f"✅ 从 .env.remote 文件读取了 {len(env_vars)} 个环境变量")
    else:
        print("⚠️  .env.remote 文件不存在，将使用默认配置")
    return env_vars

def check_images():
    """检查必要的 Docker 镜像是否存在"""
    print("\n" + "=" * 50)
    print("检查必要的 Docker 镜像")
    print("=" * 50)
    
    required_images = [
        "ghcr.io/microsoft/magentic-ui-python-env:0.0.1",
        "ghcr.io/microsoft/magentic-ui-browser:0.0.1"
    ]
    
    try:
        response = requests.get(f"{DOCKER_API_URL}/images/json", timeout=30)
        if response.status_code == 200:
            images = response.json()
            image_names = []
            for image in images:
                tags = image.get('RepoTags', [])
                if tags:
                    image_names.extend(tags)
            
            missing_images = []
            for image in required_images:
                if image not in image_names:
                    missing_images.append(image)
            
            if missing_images:
                print("❌ 缺少必要的 Docker 镜像:")
                for image in missing_images:
                    print(f"  - {image}")
                print("\n请先拉取镜像:")
                print("  python pull_remote_images.py")
                return False
            else:
                print("✅ 所需镜像已存在")
                return True
        else:
            print(f"❌ 获取镜像列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 检查镜像出错: {e}")
        return False

def create_magentic_ui_container(env_vars):
    """创建 Magentic-UI 容器"""
    print("\n" + "=" * 50)
    print("创建 Magentic-UI 容器")
    print("=" * 50)
    
    # 容器配置
    container_config = {
        "Image": "ghcr.io/microsoft/magentic-ui-python-env:0.0.1",
        "Env": [f"{k}={v}" for k, v in env_vars.items()],
        "HostConfig": {
            "PortBindings": {
                "8081/tcp": [{"HostPort": "8081"}]
            },
            "Binds": [
                "/var/run/docker.sock:/var/run/docker.sock"
            ]
        },
        "ExposedPorts": {
            "8081/tcp": {}
        },
        "WorkingDir": "/workspace",
        "Cmd": ["python", "-m", "magentic_ui"]
    }
    
    try:
        # 先尝试删除已存在的同名容器
        requests.delete(f"{DOCKER_API_URL}/containers/magentic-ui?force=true", timeout=10)
        
        response = requests.post(
            f"{DOCKER_API_URL}/containers/create?name=magentic-ui",
            headers={"Content-Type": "application/json"},
            data=json.dumps(container_config),
            timeout=30
        )
        
        if response.status_code == 201:
            result = response.json()
            container_id = result.get('Id')
            print(f"✅ Magentic-UI 容器创建成功!")
            print(f"容器 ID: {container_id[:12]}")
            return container_id
        else:
            print(f"❌ 创建容器失败: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"❌ 创建容器出错: {e}")
        return None

def create_browser_container():
    """创建浏览器容器"""
    print("\n" + "=" * 50)
    print("创建浏览器容器")
    print("=" * 50)
    
    # 浏览器容器配置
    container_config = {
        "Image": "ghcr.io/microsoft/magentic-ui-browser:0.0.1",
        "HostConfig": {
            "PortBindings": {
                "6080/tcp": [{"HostPort": "6080"}]
            }
        },
        "ExposedPorts": {
            "6080/tcp": {}
        },
        "Cmd": ["/app/start.sh"]
    }
    
    try:
        # 先尝试删除已存在的同名容器
        requests.delete(f"{DOCKER_API_URL}/containers/magentic-ui-browser?force=true", timeout=10)
        
        response = requests.post(
            f"{DOCKER_API_URL}/containers/create?name=magentic-ui-browser",
            headers={"Content-Type": "application/json"},
            data=json.dumps(container_config),
            timeout=30
        )
        
        if response.status_code == 201:
            result = response.json()
            container_id = result.get('Id')
            print(f"✅ 浏览器容器创建成功!")
            print(f"容器 ID: {container_id[:12]}")
            return container_id
        else:
            print(f"❌ 创建浏览器容器失败: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"❌ 创建浏览器容器出错: {e}")
        return None

def start_container(container_id_or_name):
    """启动容器"""
    try:
        response = requests.post(
            f"{DOCKER_API_URL}/containers/{container_id_or_name}/start",
            timeout=30
        )
        
        if response.status_code == 204:
            print(f"✅ 容器 {container_id_or_name} 启动成功!")
            return True
        elif response.status_code == 304:
            print(f"ℹ️  容器 {container_id_or_name} 已在运行")
            return True
        else:
            print(f"❌ 启动容器 {container_id_or_name} 失败: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"❌ 启动容器 {container_id_or_name} 出错: {e}")
        return False

def check_container_status(container_id_or_name):
    """检查容器状态"""
    try:
        response = requests.get(
            f"{DOCKER_API_URL}/containers/{container_id_or_name}/json",
            timeout=10
        )
        
        if response.status_code == 200:
            container_info = response.json()
            status = container_info['State']['Status']
            print(f"容器 {container_id_or_name} 状态: {status}")
            return status == 'running'
        else:
            print(f"获取容器 {container_id_or_name} 状态失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"获取容器 {container_id_or_name} 状态出错: {e}")
        return False

def main():
    """主函数"""
    print("Magentic-UI 远程服务启动脚本")
    print("=" * 60)
    
    # 检查 Docker API 连接
    if not check_docker_api():
        print("❌ 无法连接到远程 Docker API，请检查配置")
        return 1
    
    # 读取环境变量
    env_vars = read_env_file()
    
    # 检查镜像
    if not check_images():
        print("❌ 镜像检查失败")
        return 1
    
    # 创建并启动 Magentic-UI 容器
    print("\n" + "=" * 60)
    print("启动 Magentic-UI 服务")
    print("=" * 60)
    
    magentic_ui_container = create_magentic_ui_container(env_vars)
    if not magentic_ui_container:
        print("❌ Magentic-UI 容器创建失败")
        return 1
    
    if not start_container(magentic_ui_container):
        print("❌ Magentic-UI 容器启动失败")
        return 1
    
    # 创建并启动浏览器容器
    print("\n" + "=" * 60)
    print("启动浏览器服务")
    print("=" * 60)
    
    browser_container = create_browser_container()
    if not browser_container:
        print("❌ 浏览器容器创建失败")
        return 1
    
    if not start_container(browser_container):
        print("❌ 浏览器容器启动失败")
        return 1
    
    # 等待服务启动
    print("\n等待服务启动...")
    time.sleep(15)
    
    # 检查容器状态
    print("\n" + "=" * 50)
    print("检查服务状态")
    print("=" * 50)
    
    magentic_ui_running = check_container_status(magentic_ui_container)
    browser_running = check_container_status(browser_container)
    
    print("\n" + "=" * 60)
    if magentic_ui_running and browser_running:
        print("🎉 所有服务启动成功!")
        print("\n访问地址:")
        print("- Magentic-UI 界面: http://localhost:8081")
        print("- 浏览器界面: http://localhost:6080")
        print("\n使用以下脚本管理服务:")
        print("- 停止服务: python stop_remote_services.py")
        print("- 查看状态: python check_remote_status.py")
        print("- 查看日志: python view_remote_logs.py")
    else:
        print("⚠️  部分服务启动失败，请检查日志")
        if not magentic_ui_running:
            print("- Magentic-UI 服务未正常运行")
        if not browser_running:
            print("- 浏览器服务未正常运行")
    
    return 0

if __name__ == "__main__":
    exit(main())
