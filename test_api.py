#!/usr/bin/env python3
"""
测试 ModelScope API 配置
"""

import requests
import yaml

def test_modelscope_api():
    """测试 ModelScope API 连接"""
    try:
        # 读取配置文件
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 获取 API 配置
        model_config = config['model_config_light']
        api_key = model_config['config']['api_key']
        base_url = model_config['config']['base_url']
        model = model_config['config']['model']
        
        print(f"测试 ModelScope API...")
        print(f"Base URL: {base_url}")
        print(f"Model: {model}")
        print(f"API Key: {api_key[:20]}...")
        
        # 测试 API 调用
        response = requests.post(
            f"{base_url}/chat/completions",
            headers={
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            },
            json={
                'model': model,
                'messages': [
                    {'role': 'user', 'content': 'Hello, please respond with "API test successful"'}
                ],
                'max_tokens': 20
            },
            timeout=30
        )
        
        print(f"HTTP Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API 测试成功!")
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"响应内容: {content}")
            return True
        else:
            print(f"❌ API 测试失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API 测试出错: {e}")
        return False

if __name__ == "__main__":
    test_modelscope_api()
